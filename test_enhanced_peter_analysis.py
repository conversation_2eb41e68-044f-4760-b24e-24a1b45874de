#!/usr/bin/env python3
"""
Test script to verify <PERSON>'s enhanced layout analysis provides crisp, 
specific feedback about font changes and tabular differences.
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Document_Validation_Agentic import DocumentValidationAgent

async def test_enhanced_peter_analysis():
    """Test Peter's enhanced layout analysis capabilities"""
    
    print("🎯 Testing Peter's Enhanced Layout Analysis")
    print("=" * 60)
    
    # Use existing sample files
    sample_pdf = "sample/WC4803231023.pdf"
    generated_pdf = "generated/Doc1.pdf"
    
    if not os.path.exists(sample_pdf) or not os.path.exists(generated_pdf):
        print("❌ Sample PDF files not found. Please ensure files exist:")
        print(f"   📄 {sample_pdf}")
        print(f"   📄 {generated_pdf}")
        return
    
    # Initialize agent in offline mode for testing
    agent = DocumentValidationAgent(offline_mode=True)
    
    print("📖 Extracting PDF content and font information...")
    
    # Extract content
    sample_content = await agent.pdf_processor.extract_content_with_layout(sample_pdf)
    generated_content = await agent.pdf_processor.extract_content_with_layout(generated_pdf)
    
    # Extract font information
    sample_fonts = await agent.pdf_processor.extract_font_information(sample_pdf)
    generated_fonts = await agent.pdf_processor.extract_font_information(generated_pdf)
    
    print(f"✅ Sample PDF: {len(sample_content)} characters, {len(sample_fonts.get('fonts_used', []))} fonts")
    print(f"✅ Generated PDF: {len(generated_content)} characters, {len(generated_fonts.get('fonts_used', []))} fonts")
    
    # Test 1: Enhanced Font Analysis
    print("\n🔤 TEST 1: Enhanced Font Change Detection")
    print("-" * 50)
    
    font_changes = agent._find_text_font_changes(sample_fonts, generated_fonts)
    
    if font_changes:
        print(f"📊 Found {len(font_changes)} font changes:")
        for i, change in enumerate(font_changes[:5], 1):
            print(f"\n  {i}. Text: '{change['text']}'")
            print(f"     Page: {change['page']}")
            print(f"     Position: {change['position']}")
            print(f"     Changes: {change['changes']}")
            if change.get('element_type') == 'critical_static_text':
                print(f"     🚨 CRITICAL STATIC TEXT")
    else:
        print("✅ No font changes detected")
    
    # Test 2: Enhanced Table Structure Analysis
    print("\n📊 TEST 2: Enhanced Table Structure Analysis")
    print("-" * 50)
    
    table_differences = agent._analyze_tabular_structure(sample_content, generated_content)
    
    if table_differences:
        print(f"📋 Found {len(table_differences)} table differences:")
        for i, diff in enumerate(table_differences[:5], 1):
            print(f"\n  {i}. Type: {diff['difference_type']}")
            print(f"     Location: {diff['location']}")
            print(f"     Sample: {diff['sample_layout']}")
            print(f"     Generated: {diff['generated_layout']}")
            print(f"     Severity: {diff['severity']}")
            print(f"     Peter's Comment: {diff['peter_comment']}")
    else:
        print("✅ No table structure differences detected")
    
    # Test 3: Complete Layout Analysis
    print("\n🏗️ TEST 3: Complete Enhanced Layout Analysis")
    print("-" * 50)
    
    layout_analysis = await agent._analyze_layout_differences(sample_content, generated_content)
    
    print(f"📊 Peter's Analysis Results:")
    print(f"   Agent: {layout_analysis.get('agent_name', 'Unknown')}")
    print(f"   Confidence: {layout_analysis.get('agent_confidence', 0)}/100")
    print(f"   Layout Score: {layout_analysis.get('layout_score', 0)}/100")
    print(f"   Structural Consistency: {layout_analysis.get('structural_consistency', 'Unknown')}")
    
    # Show categorized differences
    differences = layout_analysis.get('layout_differences', [])
    print(f"\n🎯 Layout Differences by Category ({len(differences)} total):")
    
    # Group by difference type
    diff_categories = {}
    for diff in differences:
        category = diff.get('difference_type', 'Unknown')
        if category not in diff_categories:
            diff_categories[category] = []
        diff_categories[category].append(diff)
    
    for category, diffs in diff_categories.items():
        print(f"\n   📋 {category}: {len(diffs)} issues")
        for diff in diffs[:2]:  # Show first 2 of each type
            print(f"      • {diff.get('peter_comment', 'No comment')}")
        if len(diffs) > 2:
            print(f"      ... and {len(diffs) - 2} more")
    
    # Test 4: Specific Font Change Examples
    print("\n🔍 TEST 4: Specific Font Change Examples")
    print("-" * 50)
    
    # Look for specific critical text font changes
    critical_texts = [
        "THIS ENDORSEMENT CHANGES THE POLICY",
        "WORKERS COMPENSATION",
        "POLICY NUMBER"
    ]
    
    for text in critical_texts:
        sample_font_info = agent._find_element_font_info(text, 
            agent._group_chars_into_text_blocks_with_position(
                sample_fonts.get('page_fonts', {}).get(1, {}).get('char_details', [])))
        generated_font_info = agent._find_element_font_info(text,
            agent._group_chars_into_text_blocks_with_position(
                generated_fonts.get('page_fonts', {}).get(1, {}).get('char_details', [])))
        
        if sample_font_info and generated_font_info:
            if (sample_font_info['font'] != generated_font_info['font'] or 
                sample_font_info.get('size') != generated_font_info.get('size')):
                print(f"   🚨 '{text}':")
                print(f"      Sample: {sample_font_info['font']} {sample_font_info.get('size')}pt")
                print(f"      Generated: {generated_font_info['font']} {generated_font_info.get('size')}pt")
                print(f"      Position: {sample_font_info['position']}")
            else:
                print(f"   ✅ '{text}': No font changes")
        elif sample_font_info:
            print(f"   ⚠️ '{text}': Found in sample but not in generated")
        elif generated_font_info:
            print(f"   ⚠️ '{text}': Found in generated but not in sample")
        else:
            print(f"   ❌ '{text}': Not found in either document")
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Font Changes: {len(font_changes)} detected")
    print(f"   Table Differences: {len(table_differences)} detected")
    print(f"   Total Layout Issues: {len(differences)} detected")
    print(f"   Peter's Overall Score: {layout_analysis.get('layout_score', 0)}/100")
    
    print(f"\n💡 Peter's Recommendation:")
    print(f"   {layout_analysis.get('peter_expert_recommendation', 'No recommendation available')}")

if __name__ == "__main__":
    asyncio.run(test_enhanced_peter_analysis())
