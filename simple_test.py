#!/usr/bin/env python3
"""Simple requests test"""

import requests
import os

# Load environment variables
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    print("⚠️ load_env module not found - using system environment variables")

def test_requests():
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No API key found")
        return
    
    print(f"🔑 Using API key: {api_key[:10]}...{api_key[-10:]}")
    
    try:
        headers = {'Authorization': f'Bearer {api_key}'}
        print("🌐 Making request to OpenAI API...")
        
        r = requests.get(
            'https://api.openai.com/v1/models', 
            headers=headers, 
            timeout=10
        )
        
        print(f"✅ Status: {r.status_code}")
        if r.status_code == 200:
            print("✅ Request successful!")
            data = r.json()
            print(f"   Found {len(data.get('data', []))} models")
        else:
            print(f"❌ Request failed: {r.text[:200]}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_requests()
