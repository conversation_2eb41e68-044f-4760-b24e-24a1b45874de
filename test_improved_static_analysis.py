#!/usr/bin/env python3
"""
Test script to verify the improved static text analysis can detect differences
like "ENDOORSEMENT" vs "ENDORSEMENT" that were previously missed.
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Document_Validation_Agentic import DocumentValidationAgent

async def test_static_text_detection():
    """Test the improved static text analysis with specific examples"""
    
    print("🧪 Testing Improved Static Text Analysis")
    print("=" * 60)
    
    # Initialize the agent in offline mode to test the improved logic
    agent = DocumentValidationAgent(offline_mode=True)
    
    # Test case 1: The original issue - ENDOORSEMENT vs ENDORSEMENT
    print("\n📝 Test Case 1: ENDOORSEMENT vs ENDORSEMENT")
    print("-" * 40)
    
    sample_content = """
POLICY NUMBER: WCP000100020
NCCI INS. CO. NUMBER: 11037
THIS ENDORSEMENT CHANGES THE POLICY
PLEASE READ IT CAREFULLY
WISCONSIN DESIGNATED SURPLUS LINES INSURER
"""
    
    generated_content = """
POLICY NUMBER: WCP000100020
NCCI INS. CO. NUMBER: 11037
THIS ENDOORSEMENT CHANGE THE POLICY
PLEASE READ IT CAREFULLY
WISCONSIN DESIGNATED SURPLUS LINES INSURER
"""
    
    # Test the improved static text analysis
    static_analysis = await agent._analyze_static_text(sample_content, generated_content)
    
    print(f"✅ Static Text Score: {static_analysis.get('static_text_score', 0)}/100")
    print(f"✅ Differences Found: {len(static_analysis.get('static_differences', []))}")
    
    differences = static_analysis.get('static_differences', [])
    if differences:
        print("\n🔍 Detected Differences:")
        for i, diff in enumerate(differences, 1):
            print(f"  {i}. Type: {diff.get('type', 'unknown')}")
            print(f"     Sample: '{diff.get('sample_text', '')}'")
            print(f"     Generated: '{diff.get('generated_text', '')}'")
            print(f"     Severity: {diff.get('severity', 'unknown')}")
            print(f"     Location: {diff.get('location', 'unknown')}")
            print()
    else:
        print("❌ No differences detected - this indicates the issue still exists!")
    
    # Test case 2: Verify dynamic content is still properly excluded
    print("\n📝 Test Case 2: Dynamic Content Exclusion")
    print("-" * 40)
    
    sample_content_2 = """
Named Insured: GREAT WEST CASUALTY COMPANY
Policy Number: WCP000100020
Effective Date: 01/01/2024
Premium Amount: $1,250.00
"""
    
    generated_content_2 = """
Named Insured: DIFFERENT COMPANY NAME
Policy Number: WCP000100021
Effective Date: 01/02/2024
Premium Amount: $1,350.00
"""
    
    static_analysis_2 = await agent._analyze_static_text(sample_content_2, generated_content_2)
    
    print(f"✅ Static Text Score: {static_analysis_2.get('static_text_score', 0)}/100")
    print(f"✅ Differences Found: {len(static_analysis_2.get('static_differences', []))}")
    
    differences_2 = static_analysis_2.get('static_differences', [])
    if differences_2:
        print("\n🔍 Detected Differences:")
        for i, diff in enumerate(differences_2, 1):
            print(f"  {i}. Sample: '{diff.get('sample_text', '')}'")
            print(f"     Generated: '{diff.get('generated_text', '')}'")
    else:
        print("✅ No static differences detected - dynamic content properly excluded!")
    
    # Test case 3: Verify legitimate static text differences are caught
    print("\n📝 Test Case 3: Legitimate Static Text Differences")
    print("-" * 40)
    
    sample_content_3 = """
WORKERS COMPENSATION AND EMPLOYERS LIABILITY INSURANCE POLICY
THIS ENDORSEMENT CHANGES THE POLICY
CANCELLATION ENDORSEMENT
Coverage under this policy is cancelled
"""
    
    generated_content_3 = """
WORKERS COMPENSATION AND EMPLOYERS LIABILITY INSURANCE POLICY
THIS ENDORSEMENT MODIFIES THE POLICY
TERMINATION ENDORSEMENT
Coverage under this policy is terminated
"""
    
    static_analysis_3 = await agent._analyze_static_text(sample_content_3, generated_content_3)
    
    print(f"✅ Static Text Score: {static_analysis_3.get('static_text_score', 0)}/100")
    print(f"✅ Differences Found: {len(static_analysis_3.get('static_differences', []))}")
    
    differences_3 = static_analysis_3.get('static_differences', [])
    if differences_3:
        print("\n🔍 Detected Differences:")
        for i, diff in enumerate(differences_3, 1):
            print(f"  {i}. Sample: '{diff.get('sample_text', '')}'")
            print(f"     Generated: '{diff.get('generated_text', '')}'")
            print(f"     Severity: {diff.get('severity', 'unknown')}")
    
    # Test the individual functions directly
    print("\n📝 Test Case 4: Direct Function Testing")
    print("-" * 40)
    
    # Test _contains_dynamic_content function
    test_lines = [
        "THIS ENDORSEMENT CHANGES THE POLICY",  # Should be static
        "THIS ENDOORSEMENT CHANGE THE POLICY",  # Should be static
        "Policy Number: WCP000100020",          # Should be dynamic
        "Named Insured: GREAT WEST CASUALTY",   # Should be dynamic
        "WORKERS COMPENSATION POLICY",          # Should be static
        "Premium: $1,250.00",                   # Should be dynamic
    ]
    
    print("Dynamic Content Detection Results:")
    for line in test_lines:
        is_dynamic = agent._contains_dynamic_content(line)
        print(f"  '{line}' -> {'DYNAMIC' if is_dynamic else 'STATIC'}")
    
    # Test _is_static_text_difference function
    print("\nStatic Text Difference Detection:")
    test_pairs = [
        ("THIS ENDORSEMENT CHANGES THE POLICY", "THIS ENDOORSEMENT CHANGE THE POLICY"),
        ("CANCELLATION ENDORSEMENT", "TERMINATION ENDORSEMENT"),
        ("Policy Number: WCP000100020", "Policy Number: WCP000100021"),  # Should not be flagged
    ]
    
    for sample, generated in test_pairs:
        is_static_diff = agent._is_static_text_difference(sample, generated)
        print(f"  '{sample}' vs '{generated}' -> {'DIFFERENCE' if is_static_diff else 'NO DIFFERENCE'}")

if __name__ == "__main__":
    asyncio.run(test_static_text_detection())
