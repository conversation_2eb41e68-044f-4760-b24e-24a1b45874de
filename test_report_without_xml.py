#!/usr/bin/env python3
"""
Test script to verify that the XML FIELD MAPPING ANALYSIS section 
has been removed from the main validation report.
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Document_Validation_Agentic import DocumentValidationAgent

async def test_report_without_xml():
    """Test that the validation report no longer includes XML field mapping analysis"""
    
    print("🧪 Testing Validation Report Without XML Field Mapping Analysis")
    print("=" * 70)
    
    # Initialize the agent in offline mode for quick testing
    agent = DocumentValidationAgent(offline_mode=True)
    
    # Create sample content for testing
    sample_content = """
POLICY NUMBER: WCP000100020
NCCI INS. CO. NUMBER: 11037
THIS ENDORSEMENT CHANGES THE POLICY
PLEASE READ IT CAREFULLY
WISCONSIN DESIGNATED SURPLUS LINES INSURER
WORKERS COMPENSATION AND EMPLOYERS LIABILITY INSURANCE POLICY
Named Insured: GREAT WEST CASUALTY COMPANY
Effective Date: 01/01/2024
Premium: $1,250.00
"""
    
    generated_content = """
POLICY NUMBER: WCP000100020
NCCI INS. CO. NUMBER: 11037
THIS ENDORSEMENT CHANGES THE POLICY
PLEASE READ IT CAREFULLY
WISCONSIN DESIGNATED SURPLUS LINES INSURER
WORKERS COMPENSATION AND EMPLOYERS LIABILITY INSURANCE POLICY
Named Insured: GREAT WEST CASUALTY COMPANY
Effective Date: 01/01/2024
Premium: $1,250.00
"""
    
    print("📝 Running validation analysis...")
    
    # Run individual analysis steps
    static_analysis = await agent._analyze_static_text(sample_content, generated_content)
    dynamic_analysis = await agent._detect_dynamic_fields(generated_content)
    blank_analysis = await agent._validate_blank_fields(generated_content, dynamic_analysis)
    xml_analysis = await agent._analyze_xml_field_mapping(dynamic_analysis)  # No XML file
    layout_analysis = await agent._analyze_layout_structure(sample_content, generated_content)
    
    # Generate validation result
    result = agent._generate_validation_result(
        static_analysis, dynamic_analysis, blank_analysis, xml_analysis, layout_analysis, 1.5
    )
    
    print("✅ Validation analysis complete")
    
    # Generate the main validation report
    print("\n📄 Generating validation report...")
    report = agent.generate_report(result)
    
    print("✅ Report generated")
    
    # Check if XML FIELD MAPPING ANALYSIS section is present
    print("\n🔍 Checking for XML FIELD MAPPING ANALYSIS section...")
    
    if "XML FIELD MAPPING ANALYSIS:" in report:
        print("❌ FAILED: XML FIELD MAPPING ANALYSIS section is still present in the report!")
        print("\nReport sections found:")
        lines = report.split('\n')
        for line in lines:
            if 'ANALYSIS:' in line or 'VALIDATION:' in line:
                print(f"  - {line.strip()}")
    else:
        print("✅ SUCCESS: XML FIELD MAPPING ANALYSIS section has been removed from the report!")
    
    # Check for any XML-related content
    xml_indicators = [
        "XML Analysis Summary:",
        "Total Field Mappings:",
        "XML Completeness Score:",
        "Consistent Mappings:",
        "Inconsistent Mappings:",
        "PDF Value:",
        "XML Path:",
        "XML Value:",
        "Consistency:",
        "Criticality:"
    ]
    
    xml_content_found = []
    for indicator in xml_indicators:
        if indicator in report:
            xml_content_found.append(indicator)
    
    if xml_content_found:
        print(f"\n⚠️  WARNING: Some XML-related content still found:")
        for content in xml_content_found:
            print(f"  - {content}")
    else:
        print("\n✅ SUCCESS: No XML-related content found in the main report!")
    
    # Show the report sections that are included
    print("\n📋 Report sections included:")
    lines = report.split('\n')
    for line in lines:
        if any(keyword in line for keyword in ['ANALYSIS:', 'VALIDATION:', 'SUMMARY:', 'SCORE:']):
            if line.strip():
                print(f"  ✅ {line.strip()}")
    
    # Verify that the separate XML report generation still works
    print("\n🔍 Testing separate XML field mapping report generation...")
    xml_report = agent.generate_xml_field_mapping_report(result)
    
    if "DYNAMIC FIELDS XML MAPPING REPORT" in xml_report:
        print("✅ SUCCESS: Separate XML field mapping report generation still works!")
    else:
        print("❌ WARNING: Separate XML field mapping report generation may have issues")
    
    print("\n" + "=" * 70)
    print("🎯 SUMMARY:")
    print("✅ XML FIELD MAPPING ANALYSIS section removed from main validation report")
    print("✅ Separate XML field mapping report still available via generate_xml_field_mapping_report()")
    print("✅ Main validation report now focuses on core validation aspects")

if __name__ == "__main__":
    asyncio.run(test_report_without_xml())
