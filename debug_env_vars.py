#!/usr/bin/env python3
"""
Debug environment variables to understand the difference
"""

import os

def check_env_vars():
    print("🔍 ENVIRONMENT VARIABLE DEBUG")
    print("=" * 50)
    
    # Check all possible Azure API Management variables
    subscription_key = os.getenv('SUBSCRIPTION_KEY')
    ocp_key = os.getenv('OCP_APIM_SUBSCRIPTION_KEY')
    api_url = os.getenv('API_URL')
    azure_url = os.getenv('AZURE_API_URL')
    openai_key = os.getenv('OPENAI_API_KEY')
    
    print(f"SUBSCRIPTION_KEY: {subscription_key[:8] + '...' if subscription_key else 'NOT SET'}")
    print(f"OCP_APIM_SUBSCRIPTION_KEY: {ocp_key[:8] + '...' if ocp_key else 'NOT SET'}")
    print(f"API_URL: {api_url if api_url else 'NOT SET'}")
    print(f"AZURE_API_URL: {azure_url if azure_url else 'NOT SET'}")
    print(f"OPENAI_API_KEY: {openai_key[:8] + '...' if openai_key else 'NOT SET'}")
    
    # Check what the main script logic would detect
    subscription_key_detected = subscription_key or ocp_key
    custom_api_url_detected = api_url or azure_url
    
    print("\n🎯 DETECTION LOGIC RESULTS:")
    print(f"Azure subscription key detected: {bool(subscription_key_detected)}")
    print(f"Azure API URL detected: {bool(custom_api_url_detected)}")
    print(f"Would use Azure API: {bool(subscription_key_detected and custom_api_url_detected)}")
    
    if subscription_key_detected and custom_api_url_detected:
        print(f"✅ Azure API Management would be used")
        print(f"   Key: {subscription_key_detected[:8]}...")
        print(f"   URL: {custom_api_url_detected}")
    else:
        print(f"❌ Would fall back to OpenAI API")

if __name__ == "__main__":
    check_env_vars()
