#!/usr/bin/env python3
"""
Debug Azure configuration loading
"""

import os

def debug_azure_config():
    print("🔍 DEBUGGING AZURE CONFIG LOADING")
    print("=" * 50)
    
    try:
        if os.path.exists('test_azure_config.py'):
            print("✅ test_azure_config.py exists")
            with open('test_azure_config.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            print(f"📄 File content length: {len(content)} characters")
            print("📄 First 500 characters:")
            print(content[:500])
            print("\n" + "="*50)
            
            # Extract the actual values (basic parsing)
            subscription_key = None
            api_url = None
            
            print("🔍 Parsing lines:")
            for line_num, line in enumerate(content.split('\n'), 1):
                if 'SUBSCRIPTION_KEY =' in line:
                    print(f"Line {line_num}: {line}")
                    if '"' in line:
                        subscription_key = line.split('"')[1]
                        print(f"   Extracted key: {subscription_key[:8]}...")
                elif 'API_URL =' in line:
                    print(f"Line {line_num}: {line}")
                    if '"' in line:
                        api_url = line.split('"')[1]
                        print(f"   Extracted URL: {api_url}")
            
            print(f"\n🎯 RESULTS:")
            print(f"Subscription key: {subscription_key[:8] + '...' if subscription_key else 'NOT FOUND'}")
            print(f"API URL: {api_url if api_url else 'NOT FOUND'}")
            
            if subscription_key and api_url and subscription_key != "your-actual-subscription-key-here":
                print("✅ Valid configuration found!")
                return subscription_key, api_url
            else:
                print("❌ Configuration not valid or placeholder values")
                return None, None
                
        else:
            print("❌ test_azure_config.py does not exist")
            return None, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None

if __name__ == "__main__":
    debug_azure_config()
