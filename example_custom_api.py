#!/usr/bin/env python3
"""
Example: Using Document Validation with Custom API URL
=====================================================

This example shows how to configure the document validation system
to use a custom OpenAI API URL instead of the default endpoint.

Common use cases:
1. Azure OpenAI Service
2. Corporate proxy servers
3. OpenAI-compatible API services
4. Local LLM servers (Ollama, LM Studio)
"""

import os
import asyncio
from Document_Validation_Agentic import DocumentValidationAgent, test_openai_connection

def setup_custom_api():
    """Configure custom API settings"""
    
    print("🔧 CUSTOM API CONFIGURATION")
    print("=" * 50)
    
    # Example configurations for different scenarios:
    
    # 1. Azure OpenAI Service
    # os.environ['OPENAI_API_URL'] = 'https://your-resource-name.openai.azure.com/'
    # os.environ['OPENAI_API_KEY'] = 'your-azure-api-key'
    
    # 2. Corporate Proxy
    # os.environ['OPENAI_API_URL'] = 'https://your-company-proxy.com/openai/v1'
    # os.environ['OPENAI_API_KEY'] = 'your-openai-key'
    
    # 3. Local LLM Server (Ollama example)
    # os.environ['OPENAI_API_URL'] = 'http://localhost:11434/v1'
    # os.environ['OPENAI_API_KEY'] = 'ollama'  # Ollama doesn't require real key
    
    # 4. OpenAI-compatible service
    # os.environ['OPENAI_API_URL'] = 'https://api.your-service.com/v1'
    # os.environ['OPENAI_API_KEY'] = 'your-service-api-key'
    
    # Check current configuration
    api_url = os.getenv('OPENAI_API_URL') or os.getenv('OPENAI_BASE_URL')
    api_key = os.getenv('OPENAI_API_KEY')
    
    if api_url:
        print(f"✅ Custom API URL: {api_url}")
    else:
        print("ℹ️ Using default OpenAI API URL")
        
    if api_key:
        print(f"✅ API Key: {'*' * (len(api_key) - 4)}{api_key[-4:]}")
    else:
        print("❌ No API key found")
        
    print()

async def test_custom_api():
    """Test the custom API configuration"""
    
    print("🧪 TESTING CUSTOM API CONFIGURATION")
    print("=" * 50)
    
    # Test connection
    success = await test_openai_connection()
    
    if success:
        print("✅ Custom API configuration is working!")
        return True
    else:
        print("❌ Custom API configuration failed")
        return False

async def run_validation_with_custom_api():
    """Run document validation with custom API"""
    
    print("🚀 RUNNING VALIDATION WITH CUSTOM API")
    print("=" * 50)
    
    try:
        # Initialize validation agent
        agent = DocumentValidationAgent(model_name="gpt-4o", offline_mode=False)
        
        # Auto-detect PDF files
        from Document_Validation_Agentic import auto_detect_files
        sample_pdf, generated_pdf = auto_detect_files()
        
        if not sample_pdf or not generated_pdf:
            print("❌ Could not find PDF files")
            return
            
        # Run validation
        result = await agent.validate_documents(sample_pdf, generated_pdf)
        
        # Generate report
        report = agent.generate_report(result)
        print("\n📄 VALIDATION REPORT:")
        print("=" * 60)
        print(report)
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")

def show_configuration_examples():
    """Show examples of different API configurations"""
    
    print("📋 CONFIGURATION EXAMPLES")
    print("=" * 50)
    
    examples = {
        "Azure OpenAI": {
            "OPENAI_API_URL": "https://your-resource-name.openai.azure.com/",
            "OPENAI_API_KEY": "your-azure-api-key",
            "description": "Microsoft Azure OpenAI Service"
        },
        "Corporate Proxy": {
            "OPENAI_API_URL": "https://your-company-proxy.com/openai/v1",
            "OPENAI_API_KEY": "your-openai-key", 
            "description": "Company proxy server"
        },
        "Local Ollama": {
            "OPENAI_API_URL": "http://localhost:11434/v1",
            "OPENAI_API_KEY": "ollama",
            "description": "Local Ollama server"
        },
        "Custom Service": {
            "OPENAI_API_URL": "https://api.your-service.com/v1",
            "OPENAI_API_KEY": "your-service-key",
            "description": "OpenAI-compatible service"
        }
    }
    
    for name, config in examples.items():
        print(f"\n🔧 {name}:")
        print(f"   Description: {config['description']}")
        print(f"   URL: {config['OPENAI_API_URL']}")
        print(f"   Key: {config['OPENAI_API_KEY']}")
        print(f"   Setup:")
        print(f"     os.environ['OPENAI_API_URL'] = '{config['OPENAI_API_URL']}'")
        print(f"     os.environ['OPENAI_API_KEY'] = '{config['OPENAI_API_KEY']}'")

async def main():
    """Main function"""
    
    print("🌐 CUSTOM API URL CONFIGURATION GUIDE")
    print("=" * 60)
    
    # Show current configuration
    setup_custom_api()
    
    # Show configuration examples
    show_configuration_examples()
    
    # Test current configuration
    print("\n" + "=" * 60)
    api_working = await test_custom_api()
    
    if api_working:
        print("\n" + "=" * 60)
        await run_validation_with_custom_api()
    else:
        print("\n💡 NEXT STEPS:")
        print("1. Set your custom API URL: os.environ['OPENAI_API_URL'] = 'your-url'")
        print("2. Set your API key: os.environ['OPENAI_API_KEY'] = 'your-key'")
        print("3. Run this script again to test")

if __name__ == "__main__":
    asyncio.run(main())
