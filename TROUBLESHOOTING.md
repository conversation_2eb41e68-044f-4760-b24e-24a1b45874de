# 🔧 Document Validation Connection Troubleshooting Guide

## Common Connection Error Solutions

### 1. **OpenAI API Key Issues**

**Symptoms:**
- "API key not found" errors
- "Authentication failed" errors
- "Invalid API key" errors

**Solutions:**
```bash
# Check if API key is set
echo $OPENAI_API_KEY

# Set API key (replace with your actual key)
export OPENAI_API_KEY="sk-your-api-key-here"

# Or add to .env file
echo "OPENAI_API_KEY=sk-your-api-key-here" >> .env
```

**Verify API Key:**
1. Go to https://platform.openai.com/api-keys
2. Check if your key is active
3. Verify you have sufficient credits
4. Ensure the key has access to GPT-4 models

### 2. **Network Connection Issues**

**Symptoms:**
- "Connection timeout" errors
- "Network unreachable" errors
- "SSL certificate" errors

**Solutions:**
```bash
# Test basic connectivity
ping api.openai.com

# Test HTTPS connectivity
curl -I https://api.openai.com/v1/models

# Check if behind corporate firewall
# Contact IT to whitelist: api.openai.com
```

### 3. **Rate Limiting Issues**

**Symptoms:**
- "Rate limit exceeded" errors
- "Too many requests" errors
- "Quota exceeded" errors

**Solutions:**
- Wait 1-2 minutes between requests
- Upgrade your OpenAI plan for higher limits
- Use exponential backoff for retries

### 4. **Model Access Issues**

**Symptoms:**
- "Model not found" errors
- "Access denied to model" errors

**Solutions:**
- Try with `gpt-3.5-turbo` instead of `gpt-4o`
- Check if your account has access to GPT-4
- Verify model name spelling

### 5. **Firewall/Proxy Issues**

**Symptoms:**
- Connection hangs or times out
- "Proxy authentication required" errors

**Solutions:**
```bash
# Configure proxy (if needed)
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080

# Or bypass proxy for OpenAI
export NO_PROXY=api.openai.com
```

## Quick Diagnostic Steps

### Step 1: Test Connection
```bash
python test_connection.py
```

### Step 2: Check Environment
```bash
python -c "import os; print('API Key:', 'SET' if os.getenv('OPENAI_API_KEY') else 'NOT SET')"
```

### Step 3: Test Simple Request
```bash
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "Hello"}], "max_tokens": 5}' \
     https://api.openai.com/v1/chat/completions
```

## Regional Restrictions

If you're in a region where OpenAI is restricted:
1. Use a VPN to connect through a supported region
2. Consider using Azure OpenAI Service
3. Try alternative models like Claude or local models

## Getting Help

If issues persist:
1. Check OpenAI status: https://status.openai.com/
2. Review OpenAI documentation: https://platform.openai.com/docs
3. Contact OpenAI support: https://help.openai.com/

## Enhanced Error Handling

The updated Document_Validation.py now includes:
- ✅ Connection timeout settings (60 seconds)
- ✅ Automatic retry logic (3 attempts)
- ✅ Detailed error messages
- ✅ Specific troubleshooting guidance
- ✅ Connection test before validation
