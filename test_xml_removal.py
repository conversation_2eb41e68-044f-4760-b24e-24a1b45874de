#!/usr/bin/env python3
"""
Simple test to verify XML FIELD MAPPING ANALYSIS is removed from main report
but still available as separate report.
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Document_Validation_Agentic import DocumentValidationAgent

async def test_xml_removal():
    """Test XML section removal from main report"""
    
    print("🧪 Testing XML Section Removal")
    print("=" * 50)
    
    # Use existing sample files for a real test
    sample_pdf = "sample/WCP000100020_01_0030137674_0000340043.pdf"
    generated_pdf = "generated/WCP000100020_01_0030137674_0000340043.pdf"
    
    if not os.path.exists(sample_pdf) or not os.path.exists(generated_pdf):
        print("❌ Sample PDF files not found. Using offline mode test...")
        agent = DocumentValidationAgent(offline_mode=True)
        
        # Create mock result for testing
        from Document_Validation_Agentic import ValidationResult
        from datetime import datetime
        
        result = ValidationResult(
            overall_score=85,
            static_text_score=90,
            dynamic_fields_count=5,
            blank_fields_count=2,
            populated_dynamic_fields=[],
            blank_dynamic_fields=[],
            validation_status="PASS",
            recommendations=["Test recommendation"],
            processing_time=1.5,
            timestamp=datetime.now().isoformat(),
            xml_field_mappings=[{"field_name": "test", "match_status": "FOUND_EXACT_MATCH"}],
            xml_completeness_score=75,
            xml_analysis_summary="Test XML analysis"
        )
    else:
        print("✅ Using real PDF files for testing...")
        agent = DocumentValidationAgent(offline_mode=True)
        result = await agent.validate_documents(sample_pdf, generated_pdf)
    
    # Generate main validation report
    print("\n📄 Generating main validation report...")
    main_report = agent.generate_report(result)
    
    # Check if XML section is removed from main report
    if "XML FIELD MAPPING ANALYSIS:" in main_report:
        print("❌ FAILED: XML FIELD MAPPING ANALYSIS still in main report!")
    else:
        print("✅ SUCCESS: XML FIELD MAPPING ANALYSIS removed from main report!")
    
    # Generate separate XML report
    print("\n📄 Generating separate XML field mapping report...")
    xml_report = agent.generate_xml_field_mapping_report(result)
    
    # Check if separate XML report works
    if "DYNAMIC FIELDS XML MAPPING REPORT" in xml_report:
        print("✅ SUCCESS: Separate XML field mapping report works!")
    else:
        print("❌ FAILED: Separate XML field mapping report not working!")
    
    # Show what sections are in the main report
    print("\n📋 Main report sections:")
    lines = main_report.split('\n')
    for line in lines:
        if 'ANALYSIS:' in line or 'VALIDATION:' in line or 'SUMMARY:' in line:
            if line.strip():
                print(f"  ✅ {line.strip()}")
    
    print(f"\n📊 Summary:")
    print(f"  Main report length: {len(main_report)} characters")
    print(f"  XML report length: {len(xml_report)} characters")
    print(f"  XML section removed from main: {'✅ YES' if 'XML FIELD MAPPING ANALYSIS:' not in main_report else '❌ NO'}")
    print(f"  Separate XML report available: {'✅ YES' if 'DYNAMIC FIELDS XML MAPPING REPORT' in xml_report else '❌ NO'}")

if __name__ == "__main__":
    asyncio.run(test_xml_removal())
