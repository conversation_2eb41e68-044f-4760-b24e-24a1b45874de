🚀 DOCUMENT VALIDATION REPORT
============================================================
Validation Date: 2025-06-08T09:32:58.580878
Processing Time: 3.31 seconds
Overall Score: 73/100
Validation Status: WARNING

📊 DETAILED SCORES:
   Static Text Compliance: 50/100
   Dynamic Field Completeness: 83/100

🔍 STATIC TEXT ANALYSIS:
   Differences Found: 10
   1. DIFFERENT_TEXT:
      Location: Line 304
      Severity: LOW
      Sample: "TENNESSEE CHANGES"
      Generated: "TENESEE CHANGES"
      Impact: Minor text variation

   2. DIFFERENT_TEXT:
      Location: Line 307
      Severity: LOW
      Sample: "MOTOR CARRIER COVERAGE FORM"
      Generated: "MATAR CARRIER COVERAGE FORM"
      Impact: Minor text variation

   3. DIFFERENT_TEXT:
      Location: Modified content (phrase match)
      Severity: HIGH
      Sample: "Various provisions in this policy restrict coverage. Read the [VISUAL_GAP] SECTION I – COVERED AUTOS"
      Generated: "B. [VISUAL_GAP] SECTION II – COVERED AUTOS LIABILITY COVERAGE"
      Impact: Important content difference that may affect document accuracy

   4. DIFFERENT_TEXT:
      Location: Modified content (phrase match)
      Severity: HIGH
      Sample: "entire policy carefully to determine rights, duties and what [VISUAL_GAP] Item Two of the Declaratio..."
      Generated: "coverage in Item Two of the Declarations, then you"
      Impact: Important content difference that may affect document accuracy

   5. DIFFERENT_TEXT:
      Location: Modified content (phrase match)
      Severity: HIGH
      Sample: "entire policy carefully to determine rights, duties and what [VISUAL_GAP] Item Two of the Declaratio..."
      Generated: "7  Specifically Described "Autos" [VISUAL_GAP] Only those "autos" described in Item Three of the Dec..."
      Impact: Important content difference that may affect document accuracy

   6. DIFFERENT_TEXT:
      Location: Modified content (phrase match)
      Severity: HIGH
      Sample: "entire policy carefully to determine rights, duties and what [VISUAL_GAP] Item Two of the Declaratio..."
      Generated: "Throughout this policy the words "you" and "your" refer to [VISUAL_GAP] following numerical symbols ..."
      Impact: Important content difference that may affect document accuracy

   7. DIFFERENT_TEXT:
      Location: Modified content (phrase match)
      Severity: HIGH
      Sample: "Throughout this policy the words "you" and "your" refer to [VISUAL_GAP] following numerical symbols ..."
      Generated: "entire policy carefully to determine rights, duties and what [VISUAL_GAP] Item Two of the Declaratio..."
      Impact: Important content difference that may affect document accuracy

   8. DIFFERENT_TEXT:
      Location: Phrase-based difference ('Two of the Declarations...')
      Severity: HIGH
      Sample: "entire policy carefully to determine rights, duties and what [VISUAL_GAP] Item Two of the Declaratio..."
      Generated: "Two of the Declarations, an "auto" you acquire will be"
      Impact: Important content difference that may affect document accuracy

   9. DIFFERENT_TEXT:
      Location: Targeted detection (Handling Of Property)
      Severity: HIGH
      Sample: "7. Man Handling Of Property"
      Generated: "from the handling of property:"
      Impact: Important content difference that may affect document accuracy

   10. DIFFERENT_TEXT:
      Location: Targeted detection (Handling Of Property)
      Severity: HIGH
      Sample: "from the handling of property:"
      Generated: "7. Man Handling Of Property"
      Impact: Important content difference that may affect document accuracy

🚨 BLANK DYNAMIC FIELDS ANALYSIS:
   Total Blank Fields: 1
   Critical Issues: 1

   1. POLICY NUMBER:
      Type: policy_number
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Policy number field is blank - contains only spaces before date placeholder (likely manually edited)
      Recommendation: Populate Policy Number with appropriate value
      Customer Impact: May affect document usability and compliance

💡 RECOMMENDATIONS:
   1. Fix 10 static text differences
   2. URGENT: Populate 1 critical blank fields
   3. Address regulatory compliance risks immediately

============================================================
🎯 NEXT STEPS:
1. Address critical blank fields immediately
2. Fix static text differences
3. Validate regulatory compliance
4. Re-run validation after fixes
============================================================