#!/usr/bin/env python3
"""
🔧 Detailed OpenAI Connection Test
Tests different methods to identify the exact issue
"""

import asyncio
import os
import sys

# Load environment variables
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    print("⚠️ load_env module not found - using system environment variables")

async def test_direct_openai():
    """Test direct OpenAI API call"""
    print("🧪 Testing direct OpenAI API...")
    
    try:
        import openai
        
        # Get API key
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("❌ No API key found")
            return False
        
        # Create client
        client = openai.OpenAI(api_key=api_key)
        
        # Test simple completion
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello! Please respond with 'Direct API test successful'"}],
            max_tokens=20,
            timeout=30
        )
        
        print(f"✅ Direct API test successful!")
        print(f"   Response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ Direct API test failed: {e}")
        return False

async def test_langchain_openai():
    """Test LangChain OpenAI integration"""
    print("\n🧪 Testing LangChain OpenAI...")
    
    try:
        from langchain_openai import ChatOpenAI
        
        # Get API key
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("❌ No API key found")
            return False
        
        # Create LangChain client
        llm = ChatOpenAI(
            model="gpt-3.5-turbo",
            temperature=0.1,
            timeout=30,
            max_retries=1,
            openai_api_key=api_key  # Explicitly pass API key
        )
        
        # Test simple completion
        response = await asyncio.to_thread(
            llm.invoke, 
            "Hello! Please respond with 'LangChain test successful'"
        )
        
        print(f"✅ LangChain test successful!")
        print(f"   Response: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ LangChain test failed: {e}")
        return False

async def test_environment():
    """Test environment configuration"""
    print("\n🧪 Testing environment configuration...")
    
    # Check API key
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print(f"✅ API key found (length: {len(api_key)})")
        print(f"   Starts with: {api_key[:10]}...")
        print(f"   Ends with: ...{api_key[-10:]}")
    else:
        print("❌ No API key found")
        return False
    
    # Check other environment variables
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    for var in proxy_vars:
        value = os.getenv(var)
        if value:
            print(f"⚠️ Proxy detected: {var}={value}")
    
    return True

async def main():
    """Main test function"""
    print("🔧 DETAILED OPENAI CONNECTION DIAGNOSTIC")
    print("=" * 60)
    
    # Test environment
    env_ok = await test_environment()
    if not env_ok:
        print("\n❌ Environment issues detected")
        return
    
    # Test direct OpenAI API
    direct_ok = await test_direct_openai()
    
    # Test LangChain
    langchain_ok = await test_langchain_openai()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"   Direct OpenAI API: {'✅ Working' if direct_ok else '❌ Failed'}")
    print(f"   LangChain OpenAI:  {'✅ Working' if langchain_ok else '❌ Failed'}")
    
    if direct_ok and not langchain_ok:
        print("\n🔍 DIAGNOSIS: LangChain configuration issue")
        print("   Try updating LangChain or using direct OpenAI API")
    elif not direct_ok and not langchain_ok:
        print("\n🔍 DIAGNOSIS: API key or network issue")
        print("   Check API key validity and network connectivity")
    elif direct_ok and langchain_ok:
        print("\n🎉 DIAGNOSIS: Everything is working!")

if __name__ == "__main__":
    asyncio.run(main())
