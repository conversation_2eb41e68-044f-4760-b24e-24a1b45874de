#!/usr/bin/env python3
"""
Test Azure API Management Integration
===================================

This script demonstrates how to configure and test the Document Validation system
with your Azure API Management endpoint using subscription keys.
"""

import os
import asyncio
from Document_Validation_Agentic import test_openai_connection, DocumentValidationAgent

def setup_azure_api_config():
    """Configure Azure API Management settings"""
    print("🔧 AZURE API MANAGEMENT CONFIGURATION")
    print("=" * 60)
    
    # Example configuration - replace with your actual values
    # os.environ['SUBSCRIPTION_KEY'] = 'your-subscription-key-here'
    # os.environ['API_URL'] = 'https://your-api-management-url.com/api/endpoint'
    
    # Check current configuration
    subscription_key = os.getenv('SUBSCRIPTION_KEY') or os.getenv('OCP_APIM_SUBSCRIPTION_KEY')
    api_url = os.getenv('API_URL') or os.getenv('AZURE_API_URL')
    
    if subscription_key:
        print(f"✅ Subscription Key: {'*' * (len(subscription_key) - 4)}{subscription_key[-4:]}")
    else:
        print("❌ No subscription key found")
        print("💡 Set with: os.environ['SUBSCRIPTION_KEY'] = 'your-key'")
        
    if api_url:
        print(f"✅ API URL: {api_url}")
    else:
        print("❌ No API URL found")
        print("💡 Set with: os.environ['API_URL'] = 'your-url'")
        
    print()
    return subscription_key and api_url

async def test_azure_api():
    """Test Azure API Management connection"""
    print("🧪 TESTING AZURE API MANAGEMENT")
    print("=" * 60)
    
    # Test the connection
    connection_ok = await test_openai_connection()
    
    if connection_ok:
        print("\n✅ Azure API Management connection successful!")
        return True
    else:
        print("\n❌ Azure API Management connection failed!")
        return False

async def test_document_validation_with_azure():
    """Test document validation using Azure API Management"""
    print("📄 TESTING DOCUMENT VALIDATION WITH AZURE API")
    print("=" * 60)
    
    try:
        # Initialize validation agent (will auto-detect Azure API config)
        agent = DocumentValidationAgent(model_name="gpt-4o", offline_mode=False)
        
        # Auto-detect PDF files
        from Document_Validation_Agentic import auto_detect_files
        sample_pdf, generated_pdf = auto_detect_files()
        
        if not sample_pdf or not generated_pdf:
            print("❌ Could not find PDF files for testing")
            print("💡 Make sure you have PDFs in sample/ and generated/ folders")
            return False
            
        print(f"📄 Testing with:")
        print(f"   Sample: {sample_pdf}")
        print(f"   Generated: {generated_pdf}")
        print()
        
        # Run validation
        result = await agent.validate_documents(sample_pdf, generated_pdf)
        
        # Generate report
        report = agent.generate_report(result)
        print("\n📊 VALIDATION RESULTS:")
        print("=" * 60)
        print(report)
        
        return True
        
    except Exception as e:
        print(f"❌ Document validation failed: {e}")
        return False

def show_configuration_examples():
    """Show configuration examples for different scenarios"""
    print("\n🔧 CONFIGURATION EXAMPLES")
    print("=" * 60)
    
    print("1. Azure API Management:")
    print("   os.environ['SUBSCRIPTION_KEY'] = 'your-subscription-key'")
    print("   os.environ['API_URL'] = 'https://your-apim.azure-api.net/api/chat'")
    print()
    
    print("2. Alternative environment variable names:")
    print("   os.environ['OCP_APIM_SUBSCRIPTION_KEY'] = 'your-subscription-key'")
    print("   os.environ['AZURE_API_URL'] = 'https://your-apim.azure-api.net/api/chat'")
    print()
    
    print("3. Your ask_gpt4_about_claim function equivalent:")
    print("   SUBSCRIPTION_KEY = os.getenv('SUBSCRIPTION_KEY')")
    print("   API_URL = os.getenv('API_URL')")
    print("   headers = {'Ocp-Apim-Subscription-Key': SUBSCRIPTION_KEY}")
    print("   payload = {'question': prompt}")
    print("   response = requests.post(API_URL, headers=headers, json=payload, verify=False)")

async def main():
    """Main test function"""
    print("🚀 AZURE API MANAGEMENT INTEGRATION TEST")
    print("=" * 70)
    
    # Step 1: Check configuration
    config_ok = setup_azure_api_config()
    
    if not config_ok:
        print("⚠️ Azure API Management not configured")
        show_configuration_examples()
        print("\n💡 Configure your API settings and run this script again")
        return
    
    # Step 2: Test API connection
    print("\n" + "=" * 70)
    api_ok = await test_azure_api()
    
    if not api_ok:
        print("\n❌ API connection test failed")
        print("💡 Check your subscription key and API URL")
        return
    
    # Step 3: Test document validation
    print("\n" + "=" * 70)
    validation_ok = await test_document_validation_with_azure()
    
    if validation_ok:
        print("\n🎉 SUCCESS! Azure API Management integration working perfectly!")
        print("✅ Your ask_gpt4_about_claim function is now integrated into the validation system")
    else:
        print("\n❌ Document validation test failed")
        print("💡 Check the error messages above for troubleshooting")

if __name__ == "__main__":
    # Example: Set your configuration here for testing
    # os.environ['SUBSCRIPTION_KEY'] = 'your-actual-subscription-key'
    # os.environ['API_URL'] = 'https://your-actual-api-url.com/endpoint'
    
    asyncio.run(main())
