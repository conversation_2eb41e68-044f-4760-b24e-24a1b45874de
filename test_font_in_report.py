#!/usr/bin/env python3
"""
Test script to verify that font changes are properly included 
in the layout analysis section of the validation report.
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Document_Validation_Agentic import DocumentValidationAgent

async def test_font_in_report():
    """Test that font changes appear in the layout analysis report"""
    
    print("🔤 Testing Font Changes in Layout Analysis Report")
    print("=" * 60)
    
    # Use existing sample files
    sample_pdf = "sample/WC4803231023.pdf"
    generated_pdf = "generated/Doc1.pdf"
    
    if not os.path.exists(sample_pdf) or not os.path.exists(generated_pdf):
        print("❌ Sample PDF files not found")
        return
    
    # Initialize agent in offline mode for testing
    agent = DocumentValidationAgent(offline_mode=True)
    
    print("📖 Running complete validation to test font reporting...")
    
    # Run complete validation
    result = await agent.validate_documents(sample_pdf, generated_pdf)
    
    print(f"✅ Validation complete")
    print(f"   Overall Score: {result.overall_score}/100")
    print(f"   Layout Score: {result.layout_score}/100")
    print(f"   Total Layout Differences: {len(result.layout_differences)}")
    
    # Check for font differences in the layout analysis
    font_differences = [d for d in result.layout_differences if 'FONT' in d.get('difference_type', '')]
    
    print(f"\n🔍 Font Differences Analysis:")
    print(f"   Font differences detected: {len(font_differences)}")
    
    if font_differences:
        print(f"\n📝 Font Differences Found:")
        for i, diff in enumerate(font_differences, 1):
            print(f"   {i}. Type: {diff.get('difference_type')}")
            print(f"      Location: {diff.get('location')}")
            print(f"      Severity: {diff.get('severity')}")
            print(f"      Peter's Comment: {diff.get('peter_comment', 'N/A')}")
            print()
    
    # Generate the actual report and check if font changes are included
    print(f"\n📄 Generating validation report...")
    report = agent.generate_report(result)
    
    # Check if font changes are mentioned in the layout section
    layout_section_start = report.find("📐 LAYOUT STRUCTURE ANALYSIS:")
    if layout_section_start != -1:
        layout_section_end = report.find("💡 RECOMMENDATIONS:", layout_section_start)
        if layout_section_end == -1:
            layout_section_end = len(report)
        
        layout_section = report[layout_section_start:layout_section_end]
        
        # Check for font-related content
        font_mentions = []
        if "FONT" in layout_section:
            font_mentions.append("FONT keyword found")
        if "font:" in layout_section.lower():
            font_mentions.append("font: changes found")
        if "🔤" in layout_section:
            font_mentions.append("Font icon found")
        if "typography" in layout_section.lower():
            font_mentions.append("Typography mentioned")
        
        print(f"\n🔍 Layout Section Analysis:")
        if font_mentions:
            print(f"   ✅ Font changes ARE included in layout section:")
            for mention in font_mentions:
                print(f"      • {mention}")
        else:
            print(f"   ❌ Font changes NOT found in layout section")
        
        # Show a snippet of the layout section
        print(f"\n📋 Layout Section Preview:")
        layout_lines = layout_section.split('\n')[:15]  # First 15 lines
        for line in layout_lines:
            if line.strip():
                print(f"   {line}")
        if len(layout_section.split('\n')) > 15:
            print(f"   ... (truncated)")
    else:
        print(f"   ❌ Layout section not found in report")
    
    # Show all layout difference types for debugging
    print(f"\n🔧 Debug: All Layout Difference Types:")
    diff_types = {}
    for diff in result.layout_differences:
        diff_type = diff.get('difference_type', 'Unknown')
        if diff_type not in diff_types:
            diff_types[diff_type] = 0
        diff_types[diff_type] += 1
    
    for diff_type, count in diff_types.items():
        print(f"   • {diff_type}: {count}")
    
    print(f"\n🎯 Summary:")
    print(f"   Total layout differences: {len(result.layout_differences)}")
    print(f"   Font-related differences: {len(font_differences)}")
    print(f"   Font changes in report: {'YES' if font_mentions else 'NO'}")

if __name__ == "__main__":
    asyncio.run(test_font_in_report())
