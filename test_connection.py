#!/usr/bin/env python3
"""
🔧 OpenAI Connection Test Script
Quick test to verify OpenAI API connection and troubleshoot issues
"""

import asyncio
import os
import sys

# Load environment variables
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    print("⚠️ load_env module not found - using system environment variables")

# Import the test function
try:
    from Document_Validation import test_openai_connection
except ImportError:
    print("❌ Could not import test function from Document_Validation.py")
    sys.exit(1)

async def main():
    """Main test function"""
    print("🔧 OPENAI CONNECTION DIAGNOSTIC TOOL")
    print("=" * 60)
    
    # Test different models
    models_to_test = ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"]
    
    for model in models_to_test:
        print(f"\n🧪 Testing model: {model}")
        print("-" * 40)
        
        success = await test_openai_connection(model)
        
        if success:
            print(f"✅ {model} is working!")
            break
        else:
            print(f"❌ {model} failed")
    
    print("\n" + "=" * 60)
    print("🏁 Connection test complete!")

if __name__ == "__main__":
    asyncio.run(main())
