#!/usr/bin/env python3
"""
Simple test to verify font changes are in the layout report.
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Document_Validation_Agentic import DocumentValidationAgent

async def simple_font_test():
    """Simple test for font changes in report"""
    
    print("Testing Font Changes in Layout Report")
    print("=" * 50)
    
    # Use existing sample files
    sample_pdf = "sample/WC4803231023.pdf"
    generated_pdf = "generated/Doc1.pdf"
    
    if not os.path.exists(sample_pdf) or not os.path.exists(generated_pdf):
        print("Sample PDF files not found")
        return
    
    # Initialize agent in offline mode
    agent = DocumentValidationAgent(offline_mode=True)
    
    print("Running validation...")
    
    # Run complete validation
    result = await agent.validate_documents(sample_pdf, generated_pdf)
    
    print(f"Validation complete")
    print(f"Layout Score: {result.layout_score}/100")
    print(f"Total Layout Differences: {len(result.layout_differences)}")
    
    # Check for font differences
    font_differences = [d for d in result.layout_differences if 'FONT' in d.get('difference_type', '')]
    print(f"Font differences detected: {len(font_differences)}")
    
    # Generate report and check for font content
    print("Generating report...")
    report = agent.generate_report(result)
    
    # Check if font changes are in the layout section
    layout_start = report.find("LAYOUT STRUCTURE ANALYSIS:")
    recommendations_start = report.find("RECOMMENDATIONS:")
    
    if layout_start != -1 and recommendations_start != -1:
        layout_section = report[layout_start:recommendations_start]
        
        font_found = False
        if "FONT" in layout_section:
            font_found = True
            print("SUCCESS: FONT found in layout section")
        
        if "font:" in layout_section.lower():
            font_found = True
            print("SUCCESS: font: found in layout section")
        
        if not font_found:
            print("ISSUE: Font changes not found in layout section")
            
        # Show first few layout differences from report
        lines = layout_section.split('\n')
        print("\nLayout section preview:")
        for i, line in enumerate(lines[:20]):
            if line.strip():
                print(f"  {line}")
    else:
        print("Could not find layout section in report")

if __name__ == "__main__":
    asyncio.run(simple_font_test())
