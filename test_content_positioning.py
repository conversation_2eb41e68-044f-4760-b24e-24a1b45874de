#!/usr/bin/env python3
"""
Test <PERSON>'s crisp content positioning analysis
"""

import asyncio
from Document_Validation_Agentic import DocumentValidationAgent

async def test_content_positioning():
    """Test <PERSON>'s detailed content positioning analysis"""
    
    print("📍 PETER'S CONTENT POSITIONING ANALYSIS")
    print("=" * 60)
    
    # Initialize agent
    agent = DocumentValidationAgent(offline_mode=True)
    
    # File paths
    sample_pdf = "sample/WC4803231023.pdf"
    generated_pdf = "generated/WC 48 03 23_WCP000100020_01_0007252874_0000340043_Gen1.pdf"
    
    try:
        # Extract content
        print("📖 Extracting content for positioning analysis...")
        sample_content = await agent.pdf_processor.extract_content_with_layout(sample_pdf)
        generated_content = await agent.pdf_processor.extract_content_with_layout(generated_pdf)
        
        # Run Peter's layout analysis
        layout_analysis = await agent._analyze_layout_differences(sample_content, generated_content)
        
        # Filter for content positioning differences
        positioning_diffs = [d for d in layout_analysis.get('layout_differences', []) 
                           if 'CONTENT_POSITIONING' in d.get('difference_type', '')]
        
        print(f"\n🎯 CONTENT POSITIONING DIFFERENCES DETECTED: {len(positioning_diffs)}")
        print("=" * 60)
        
        if positioning_diffs:
            # Show individual positioning changes
            individual_changes = [d for d in positioning_diffs if d.get('difference_type') == 'CONTENT_POSITIONING']
            summary_change = [d for d in positioning_diffs if d.get('difference_type') == 'CONTENT_POSITIONING_SUMMARY']
            
            if individual_changes:
                print("\n📋 INDIVIDUAL CONTENT MOVES:")
                print("-" * 40)
                for i, diff in enumerate(individual_changes, 1):
                    location = diff.get('location', 'Unknown')
                    sample_line = diff.get('sample_layout', 'Unknown')
                    generated_line = diff.get('generated_layout', 'Unknown')
                    impact = diff.get('impact_description', 'Unknown')
                    comment = diff.get('peter_comment', 'No comment')
                    
                    print(f"{i}. {location}")
                    print(f"   📍 Position: {sample_line} → {generated_line}")
                    print(f"   📏 Impact: {impact}")
                    print(f"   💬 Peter: {comment}")
                    print()
            
            if summary_change:
                print("📊 POSITIONING SUMMARY:")
                print("-" * 30)
                summary = summary_change[0]
                print(f"   📈 Total Elements Moved: {summary.get('impact_description', 'Unknown')}")
                print(f"   🎯 Visual Impact: {summary.get('visual_impact', 'Unknown')}")
                print(f"   📝 Details: {summary.get('peter_comment', 'No details')}")
        else:
            print("✅ No significant content positioning differences detected")
        
        # Show detailed positioning table
        print(f"\n📊 DETAILED POSITIONING TABLE:")
        print("-" * 60)
        print(f"{'Element':<20} {'Sample':<8} {'Generated':<10} {'Move':<6} {'Direction'}")
        print("-" * 60)
        
        # Extract positioning details manually for table
        sample_lines = sample_content.split('\n')
        generated_lines = generated_content.split('\n')
        
        key_elements = [
            ("POLICY NUMBER", "Policy Header"),
            ("NCCI", "NCCI Info"),
            ("THIS ENDORSEMENT CHANGES", "Main Notice"),
            ("WISCONSIN DESIGNATED", "State Designation"), 
            ("CANCELLATION ENDORSEMENT", "Endorsement Type"),
            ("WORKERS COMPENSATION", "Policy Type"),
            ("Named Insured", "Insured Table"),
            ("Endorsement Effective", "Effective Date")
        ]
        
        for element, short_desc in key_elements:
            sample_pos = -1
            generated_pos = -1
            
            for i, line in enumerate(sample_lines):
                if element.upper() in line.upper():
                    sample_pos = i + 1
                    break
            
            for i, line in enumerate(generated_lines):
                if element.upper() in line.upper():
                    generated_pos = i + 1
                    break
            
            if sample_pos != -1 and generated_pos != -1:
                move_distance = abs(sample_pos - generated_pos)
                direction = "↑ UP" if generated_pos < sample_pos else "↓ DOWN"
                status = "🚨" if move_distance > 3 else "✅"
                
                print(f"{short_desc:<20} {sample_pos:<8} {generated_pos:<10} {move_distance:<6} {direction} {status}")
            elif sample_pos != -1:
                print(f"{short_desc:<20} {sample_pos:<8} {'MISSING':<10} {'N/A':<6} {'❌ MISSING'}")
            elif generated_pos != -1:
                print(f"{short_desc:<20} {'MISSING':<8} {generated_pos:<10} {'N/A':<6} {'➕ NEW'}")
            else:
                print(f"{short_desc:<20} {'NOT FOUND':<8} {'NOT FOUND':<10} {'N/A':<6} {'❓ UNKNOWN'}")
        
        print("-" * 60)
        print("Legend: 🚨 = Significant move (>3 lines), ✅ = Minor move (≤3 lines)")
        print("        ❌ = Missing in generated, ➕ = New in generated")
        
        # Peter's professional assessment
        print(f"\n🎯 PETER'S POSITIONING ASSESSMENT:")
        print("-" * 40)
        print(f"📊 Layout Score: {layout_analysis.get('layout_score', 0)}/100")
        print(f"🏗️ Structural Consistency: {layout_analysis.get('structural_consistency', 'Unknown')}")
        
        major_moves = sum(1 for element, _ in key_elements 
                         if abs(agent._find_element_position(element, sample_lines) - 
                               agent._find_element_position(element, generated_lines)) > 3
                         if agent._find_element_position(element, sample_lines) != -1 and 
                            agent._find_element_position(element, generated_lines) != -1)
        
        print(f"📍 Major Repositioning: {major_moves} elements moved >3 lines")
        print(f"💡 Recommendation: {'Review positioning for consistency' if major_moves > 2 else 'Minor positioning adjustments needed'}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_content_positioning())
