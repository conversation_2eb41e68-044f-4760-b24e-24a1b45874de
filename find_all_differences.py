#!/usr/bin/env python3
"""
🔍 COMPREHENSIVE SEARCH: Find ALL differences between sample and generated PDFs
"""

import asyncio
from Document_Validation import EnhancedPDFProcessor, auto_detect_files

async def find_all_differences():
    """Find ALL differences between the documents, not just the reported ones"""
    
    print("🔍 COMPREHENSIVE DIFFERENCE SEARCH")
    print("=" * 60)
    print("Searching ENTIRE document for ANY differences that might have been missed")
    print()
    
    # Get the current files
    sample_pdf, generated_pdf = auto_detect_files()
    
    if not sample_pdf or not generated_pdf:
        print("❌ Could not detect files")
        return
    
    print(f"📄 Sample: {sample_pdf}")
    print(f"📄 Generated: {generated_pdf}")
    print()
    
    # Extract content with enhanced processor
    processor = EnhancedPDFProcessor()
    
    try:
        print("📖 EXTRACTING CONTENT...")
        print("-" * 40)
        
        sample_content = await processor.extract_content_with_layout(sample_pdf)
        generated_content = await processor.extract_content_with_layout(generated_pdf)
        
        print(f"✅ Sample content: {len(sample_content)} characters")
        print(f"✅ Generated content: {len(generated_content)} characters")
        print(f"Character difference: {abs(len(sample_content) - len(generated_content))}")
        print()
        
        # Split into lines for comprehensive comparison
        sample_lines = [line.strip() for line in sample_content.split('\n') if line.strip()]
        generated_lines = [line.strip() for line in generated_content.split('\n') if line.strip()]
        
        print(f"📊 LINE BREAKDOWN:")
        print(f"Sample lines: {len(sample_lines)}")
        print(f"Generated lines: {len(generated_lines)}")
        print()
        
        # COMPREHENSIVE LINE-BY-LINE COMPARISON
        print("🔍 COMPREHENSIVE LINE-BY-LINE COMPARISON:")
        print("=" * 60)
        
        all_differences = []
        max_lines = max(len(sample_lines), len(generated_lines))
        
        for i in range(max_lines):
            sample_line = sample_lines[i] if i < len(sample_lines) else ""
            generated_line = generated_lines[i] if i < len(generated_lines) else ""
            
            if sample_line != generated_line:
                all_differences.append({
                    'line_num': i + 1,
                    'sample': sample_line,
                    'generated': generated_line,
                    'page': 'Unknown'  # We'll determine page later
                })
        
        print(f"🚨 TOTAL DIFFERENCES FOUND: {len(all_differences)}")
        print()
        
        if len(all_differences) == 0:
            print("❌ NO DIFFERENCES FOUND!")
            print("This suggests:")
            print("1. The files are identical")
            print("2. Your changes weren't saved")
            print("3. The changes are in formatting/spacing that gets normalized")
            return
        
        # Determine which page each difference is on
        sample_pages = sample_content.split("--- Page ")
        generated_pages = generated_content.split("--- Page ")
        
        # Map line numbers to pages
        line_to_page_sample = {}
        line_to_page_generated = {}
        
        current_line = 0
        for page_num, page_content in enumerate(sample_pages[1:], 1):  # Skip first empty split
            page_lines = [line.strip() for line in page_content.split('\n') if line.strip()]
            for line in page_lines:
                current_line += 1
                line_to_page_sample[current_line] = page_num
        
        current_line = 0
        for page_num, page_content in enumerate(generated_pages[1:], 1):  # Skip first empty split
            page_lines = [line.strip() for line in page_content.split('\n') if line.strip()]
            for line in page_lines:
                current_line += 1
                line_to_page_generated[current_line] = page_num
        
        # Show all differences with page information
        print("📋 ALL DIFFERENCES FOUND:")
        print("=" * 60)
        
        page_5_differences = []
        
        for i, diff in enumerate(all_differences, 1):
            line_num = diff['line_num']
            sample_page = line_to_page_sample.get(line_num, 'Unknown')
            generated_page = line_to_page_generated.get(line_num, 'Unknown')
            
            print(f"\n{i}. DIFFERENCE ON LINE {line_num} (Sample Page {sample_page}, Generated Page {generated_page}):")
            print(f"   Sample:    '{diff['sample']}'")
            print(f"   Generated: '{diff['generated']}'")
            
            # Check if this is on page 5
            if sample_page == 5 or generated_page == 5:
                page_5_differences.append(diff)
                print(f"   🎯 THIS IS ON PAGE 5!")
            
            # Analyze the type of difference
            if not diff['sample']:
                print(f"   Type: Missing line in sample")
            elif not diff['generated']:
                print(f"   Type: Missing line in generated")
            else:
                # Check for word-level differences
                sample_words = diff['sample'].split()
                generated_words = diff['generated'].split()
                
                if len(sample_words) != len(generated_words):
                    print(f"   Type: Word count difference ({len(sample_words)} vs {len(generated_words)})")
                else:
                    # Find specific word differences
                    word_diffs = []
                    for j, (sw, gw) in enumerate(zip(sample_words, generated_words)):
                        if sw != gw:
                            word_diffs.append(f"'{sw}' → '{gw}'")
                    
                    if word_diffs:
                        print(f"   Type: Word changes: {', '.join(word_diffs)}")
                    else:
                        print(f"   Type: Formatting/spacing difference")
        
        print(f"\n📊 SUMMARY:")
        print(f"Total differences found: {len(all_differences)}")
        print(f"Page 5 differences: {len(page_5_differences)}")
        
        if len(page_5_differences) >= 2:
            print(f"\n🎯 THE TWO PAGE 5 DIFFERENCES YOU MENTIONED:")
            for i, diff in enumerate(page_5_differences[:2], 1):
                print(f"\n{i}. Line {diff['line_num']}:")
                print(f"   Sample:    '{diff['sample']}'")
                print(f"   Generated: '{diff['generated']}'")
        elif len(page_5_differences) == 1:
            print(f"\n⚠️ Only found 1 page 5 difference:")
            print(f"   Line {page_5_differences[0]['line_num']}:")
            print(f"   Sample:    '{page_5_differences[0]['sample']}'")
            print(f"   Generated: '{page_5_differences[0]['generated']}'")
        else:
            print(f"\n❌ No page 5 differences found!")
            print("The two changes you mentioned might be:")
            print("1. On a different page")
            print("2. Not saved to the files")
            print("3. In formatting that gets normalized during extraction")
        
        # Show differences by page
        print(f"\n📄 DIFFERENCES BY PAGE:")
        print("-" * 40)
        
        page_counts = {}
        for diff in all_differences:
            line_num = diff['line_num']
            sample_page = line_to_page_sample.get(line_num, 'Unknown')
            generated_page = line_to_page_generated.get(line_num, 'Unknown')
            
            page_key = f"Sample P{sample_page}, Generated P{generated_page}"
            page_counts[page_key] = page_counts.get(page_key, 0) + 1
        
        for page, count in sorted(page_counts.items()):
            print(f"   {page}: {count} differences")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(find_all_differences())
