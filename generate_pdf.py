#!/usr/bin/env python3
"""
PDF Generation Script for Hackathon Presentation
==============================================

This script converts the HTML presentation to a professional PDF document.
"""

import os
import sys
from pathlib import Path

def install_requirements():
    """Install required packages for PDF generation"""
    try:
        import weasyprint
        print("✅ WeasyPrint already installed")
        return True
    except ImportError:
        print("📦 Installing WeasyPrint for PDF generation...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "weasyprint"])
            print("✅ WeasyPrint installed successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to install WeasyPrint: {e}")
            return False

def generate_pdf_weasyprint():
    """Generate PDF using WeasyPrint (recommended)"""
    try:
        import weasyprint
        
        # Get current directory
        current_dir = Path(__file__).parent
        html_file = current_dir / "hackathon_presentation.html"
        pdf_file = current_dir / "Multi_Agent_Document_Validation_Hackathon_Presentation.pdf"
        
        if not html_file.exists():
            print(f"❌ HTML file not found: {html_file}")
            return False
            
        print("🔄 Generating PDF with WeasyPrint...")
        
        # Generate PDF
        html_doc = weasyprint.HTML(filename=str(html_file))
        html_doc.write_pdf(str(pdf_file))
        
        print(f"✅ PDF generated successfully: {pdf_file}")
        print(f"📄 File size: {pdf_file.stat().st_size / 1024:.1f} KB")
        return True
        
    except Exception as e:
        print(f"❌ WeasyPrint PDF generation failed: {e}")
        return False

def generate_pdf_browser():
    """Generate PDF using browser (fallback method)"""
    try:
        import webbrowser
        from pathlib import Path
        
        current_dir = Path(__file__).parent
        html_file = current_dir / "hackathon_presentation.html"
        
        if not html_file.exists():
            print(f"❌ HTML file not found: {html_file}")
            return False
            
        # Open in browser for manual PDF generation
        file_url = f"file:///{html_file.absolute()}"
        print(f"🌐 Opening in browser: {file_url}")
        print("📄 Use Ctrl+P (Cmd+P on Mac) to print to PDF")
        print("⚙️ Recommended settings:")
        print("   - Paper size: A4")
        print("   - Margins: Default")
        print("   - Background graphics: Enabled")
        print("   - Headers and footers: Disabled")
        
        webbrowser.open(file_url)
        return True
        
    except Exception as e:
        print(f"❌ Browser method failed: {e}")
        return False

def main():
    """Main function to generate PDF"""
    print("🚀 HACKATHON PRESENTATION PDF GENERATOR")
    print("=" * 60)
    
    # Check if HTML file exists
    html_file = Path("hackathon_presentation.html")
    if not html_file.exists():
        print("❌ hackathon_presentation.html not found!")
        print("Please make sure the HTML file is in the same directory.")
        return
    
    print(f"✅ Found HTML file: {html_file}")
    print(f"📄 File size: {html_file.stat().st_size / 1024:.1f} KB")
    print()
    
    # Method 1: Try WeasyPrint (best quality)
    print("🔄 Attempting PDF generation with WeasyPrint...")
    if install_requirements():
        if generate_pdf_weasyprint():
            print("\n🎉 SUCCESS! PDF generated with WeasyPrint")
            return
    
    # Method 2: Fallback to browser method
    print("\n🔄 Falling back to browser method...")
    if generate_pdf_browser():
        print("\n📝 MANUAL STEP REQUIRED:")
        print("1. Browser should open with the presentation")
        print("2. Press Ctrl+P (Cmd+P on Mac)")
        print("3. Select 'Save as PDF' as destination")
        print("4. Enable 'Background graphics' in More settings")
        print("5. Save as 'Multi_Agent_Document_Validation_Hackathon_Presentation.pdf'")
        return
    
    print("\n❌ All PDF generation methods failed")
    print("💡 Manual alternative:")
    print("1. Open hackathon_presentation.html in your browser")
    print("2. Use browser's Print to PDF feature")

if __name__ == "__main__":
    main()
