 DOCUMENT VALIDATION REPORT
============================================================
Validation Date: 2025-07-14T17:33:43.038898
Processing Time: 60.54 seconds
Overall Score: 76/100
Validation Status: WARNING

 DETAILED SCORES:
   Static Text Compliance: 95/100
   Dynamic Field Completeness: 84/100
   XML Field Mapping Completeness: 61/100

 STATIC TEXT ANALYSIS:
   Differences Found: 0
 POPULATED DYNAMIC FIELDS ANALYSIS:
   Total Populated Fields: 11

   1. POLICY NUMBER:
      Type: policy_number
      Value: WCP000100020-1
      Location: Page 1
      Page Number: 1

   2. NCCI INSURANCE COMPANY NUMBER:
      Type: ncci_number
      Value: 11037
      Location: Page 1
      Page Number: 1

   3. INSURANCE COMPANY:
      Type: company_name
      Value: GREAT WEST CASUALTY COMPANY
      Location: Page 1
      Page Number: 1

   4. E<PERSON>ORSEMENT NUMBER:
      Type: endorsement_number
      Value: 0007252874
      Location: Page 1
      Page Number: 1

   5. DATE:
      Type: date
      Value: 04/03/2025
      Location: Page 1
      Page Number: 1

   6. AGENT/BROKER:
      Type: agent_broker
      Value: 1024 ARTHUR J GALLAGHER RISK MGMT SVCS INC
      Location: Page 1
      Page Number: 1

   7. COMPANY NAME:
      Type: company_name
      Value: GREAT WEST CASUALTY COMPANY
      Location: Page 1
      Page Number: 1

   8. ORGANIZATION NAME:
      Type: organization_name
      Value: ARTHUR J GALLAGHER RISK MGMT SVCS INC
      Location: Page 1
      Page Number: 1

   9. NAMED INSURED:
      Type: named_insured
      Value: WI Wisconsin Designated
      Location: Page 1 (Tabular layout)
      Page Number: 1

   10. ENDORSEMENT EFFECTIVE DATE:
      Type: endorsement_effective_date
      Value: 04/03/2025
      Location: Page 1 (Tabular layout)
      Page Number: 1

   11. NAME AND ADDRESS OF DESIGNATED NAMED INSURED:
      Type: name_address_of_designated_named_insured
      Value: Test
      Location: Page 1
      Page Number: 1

 BLANK DYNAMIC FIELDS ANALYSIS:
   Total Blank Fields: 2
   Critical Issues: 2

   1. EFFECTIVE DATE OF CANCELLATION:
      Type: effective_date_of_cancellation
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Effective Date of Cancellation field is blank
      Recommendation: Populate Effective Date of Cancellation with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   2. NAME AND ADDRESS OF FIRST NAMED INSURED:
      Type: name_address_of_first_named_insured
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Name and Address of First Named Insured field is blank
      Recommendation: Populate Name and Address of First Named Insured with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

 XML FIELD MAPPING ANALYSIS:
   XML Analysis Summary: Searched for 13 dynamic field values in XML: 8 exact matches found, 2 blank fields, 3 values not found
   Total Field Mappings: 13
   XML Completeness Score: 61/100

   ✅ Consistent Mappings: 0
   ❌ Inconsistent Mappings: 0

   1. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: NOT_FOUND
      XML Value: N/A
      Consistency: Unknown
      Criticality: CRITICAL

   2. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: NOT_FOUND
      XML Value: N/A
      Consistency: Unknown
      Criticality: LOW

   3. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: PolicyPeriod/DisplayName
      XML Value: N/A
      Consistency: Unknown
      Criticality: LOW

   4. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: NOT_FOUND
      XML Value: N/A
      Consistency: Unknown
      Criticality: LOW

   5. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: PolicyPeriod/DisplayName
      XML Value: N/A
      Consistency: Unknown
      Criticality: LOW

   ... and 8 more field mappings

💡 RECOMMENDATIONS:
   1. URGENT: Populate 2 critical blank fields
   2. Address regulatory compliance risks immediately
   3. URGENT: Resolve 2 critical XML field mapping issues
   4. Investigate 3 field values not found in XML
   5. Address 2 blank fields in PDF
   6. Validate XML data completeness for missing values
   7. Ensure proper data synchronization between PDF generation and XML source

============================================================
🎯 NEXT STEPS:
1. Address critical blank fields immediately
2. Fix static text differences
3. Resolve XML field mapping inconsistencies
4. Validate regulatory compliance
5. Re-run validation after fixes
============================================================