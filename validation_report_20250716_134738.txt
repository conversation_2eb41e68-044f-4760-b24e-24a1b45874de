 DOCUMENT VALIDATION REPORT
============================================================
Validation Date: 2025-07-16T13:47:38.623824
Processing Time: 74.42 seconds
Overall Score: 59/100
Validation Status: FAIL

 DETAILED SCORES:
   Static Text Compliance: 60/100
   Dynamic Field Completeness: 84/100
   XML Field Mapping Completeness: 46/100
   Layout Structure Consistency: 30/100

 STATIC TEXT ANALYSIS:
   Differences Found: 5
   1. DIFFERENT_TEXT:
      Location: Content match (Sample line 10 ↔ Generated line 4)
      Severity: HIGH
      Sample: "THIS ENDORSEMENT CHANGES THE POLICY.  PLEASE READ IT CAREFULLY."
      Generated: "THIS ENDOORSEMENT CHANGE THE POLICY. PLEASE READ IT CAREFULLY."
      Impact: Important content difference that may affect document accuracy

   2. DIFFERENT_TEXT:
      Location: Line 10
      Severity: HIGH
      Sample: "THIS ENDORSEMENT CHANGES THE POLICY.  PLEASE READ IT CAREFULLY."
      Generated: "below."
      Impact: Important content difference that may affect document accuracy

   3. DIFFERENT_TEXT:
      Location: Line 14
      Severity: HIGH
      Sample: "WORKERS COMPENSATION AND EMPLOYERS LIABILITY INSURANCE POLICY"
      Generated: "HIGH WEST CASUALTY COMPANY"
      Impact: Important content difference that may affect document accuracy

   4. DIFFERENT_TEXT:
      Location: Content match (Sample line 16 ↔ Generated line 9)
      Severity: MEDIUM
      Sample: "indicated below."
      Generated: "This endorsement changes the policy effective on the inception date of the policy unless another dat..."
      Impact: Significant text difference that should be reviewed

   5. DIFFERENT_TEXT:
      Location: Line 13
      Severity: MEDIUM
      Sample: "This endorsement modifies insurance provided under the following:"
      Generated: "Endorsement Number"
      Impact: Significant text difference that should be reviewed

 POPULATED DYNAMIC FIELDS ANALYSIS:
   Total Populated Fields: 11

   1. POLICY NUMBER:
      Type: policy_number
      Value: WCP000100020-1
      Location: Page 1
      Page Number: 1

   2. NCCI INSURANCE COMPANY NUMBER:
      Type: ncci_number
      Value: 11037
      Location: Page 1
      Page Number: 1

   3. INSURANCE COMPANY:
      Type: company_name
      Value: HIGH WEST CASUALTY COMPANY
      Location: Page 1
      Page Number: 1

   4. ENDORSEMENT NUMBER:
      Type: endorsement_number
      Value: 0007252874
      Location: Page 1
      Page Number: 1

   5. DATE:
      Type: date
      Value: 04/03/2025
      Location: Page 1
      Page Number: 1

   6. AGENT/BROKER:
      Type: agent_broker
      Value: 1024 ARTHUR J GALLAGHER RISK MGMT SVCS INC
      Location: Page 1
      Page Number: 1

   7. COMPANY NAME:
      Type: company_name
      Value: HIGH WEST CASUALTY COMPANY
      Location: Page 1
      Page Number: 1

   8. ORGANIZATION NAME:
      Type: organization_name
      Value: ARTHUR J GALLAGHER RISK MGMT SVCS INC
      Location: Page 1
      Page Number: 1

   9. NAMED INSURED:
      Type: named_insured
      Value: WI Wisconsin Designated
      Location: Page 1 (Tabular layout)
      Page Number: 1

   10. ENDORSEMENT EFFECTIVE DATE:
      Type: endorsement_effective_date
      Value: 04/03/2025
      Location: Page 1 (Tabular layout)
      Page Number: 1

   11. NAME AND ADDRESS OF DESIGNATED NAMED INSURED:
      Type: name_address_of_designated_named_insured
      Value: Test
      Location: Page 1
      Page Number: 1

 BLANK DYNAMIC FIELDS ANALYSIS:
   Total Blank Fields: 2
   Critical Issues: 2

   1. EFFECTIVE DATE OF CANCELLATION:
      Type: effective_date_of_cancellation
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Effective Date of Cancellation field is blank
      Recommendation: Populate Effective Date of Cancellation with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   2. NAME AND ADDRESS OF FIRST NAMED INSURED:
      Type: name_address_of_first_named_insured
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Name and Address of First Named Insured field is blank
      Recommendation: Populate Name and Address of First Named Insured with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

📐 LAYOUT STRUCTURE ANALYSIS:
   Layout Analysis Summary: Found 8 layout differences with score 30/100
   Total Layout Differences: 11
   Layout Score: 30/100

   🚨 Critical Layout Issues: 1
   ⚠️ High Priority Issues: 9

   1. 🚨 TABLE_MISSING:
      Location: Missing table: Table at Line 9
      Sample Layout: Table present with 3 columns, 1 rows
      Generated Layout: Table not found
      Severity: CRITICAL
      Visual Impact: Critical data presentation missing
      Peter's Comment: Table 'Table at Line 9' completely missing from generated document

   2. ⚠️ DOCUMENT_LENGTH:
      Location: Overall document structure
      Sample Layout: 94 content lines
      Generated Layout: 36 content lines
      Severity: HIGH
      Visual Impact: Significant difference in document size and structure
      Peter's Comment: Document length mismatch: 58 line difference indicates structural changes

   3. ⚠️ CONTENT_POSITIONING:
      Location: NCCI Info
      Sample Layout: Line 9
      Generated Layout: Line 3
      Severity: HIGH
      Visual Impact: Content repositioned affects document flow
      Peter's Comment: NCCI Info: Line 9 → 3 (6 lines ↑)

   4. ⚠️ CONTENT_POSITIONING:
      Location: State Designation
      Sample Layout: Line 11
      Generated Layout: Line 5
      Severity: HIGH
      Visual Impact: Content repositioned affects document flow
      Peter's Comment: State Designation: Line 11 → 5 (6 lines ↑)

   5. ⚠️ CONTENT_POSITIONING:
      Location: Endorsement Type
      Sample Layout: Line 12
      Generated Layout: Line 6
      Severity: HIGH
      Visual Impact: Content repositioned affects document flow
      Peter's Comment: Endorsement Type: Line 12 → 6 (6 lines ↑)

   ... and 6 more layout differences

💡 RECOMMENDATIONS:
   1. Fix 5 static text differences
   2. URGENT: Populate 2 critical blank fields
   3. Address regulatory compliance risks immediately
   4. URGENT: Resolve 2 critical XML field mapping issues
   5. Investigate 5 field values not found in XML
   6. Address 2 blank fields in PDF
   7. Validate XML data completeness for missing values
   8. Ensure proper data synchronization between PDF generation and XML source
   9. URGENT: Fix 1 critical layout issues
   10. Review layout differences for consistency

============================================================
🎯 NEXT STEPS:
1. Address critical blank fields immediately
2. Fix static text differences
3. Resolve XML field mapping inconsistencies
4. Validate regulatory compliance
5. Re-run validation after fixes
============================================================