#!/usr/bin/env python3
"""Test the full document validation workflow with enhanced static text analysis"""

import asyncio
import sys
import os

# Load environment variables
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    print("⚠️ load_env module not found - using system environment variables")

# Import from the validation agent
from Document_Validation_Agentic import DocumentValidationAgent

async def test_full_validation():
    try:
        # Initialize the validation agent in offline mode
        agent = DocumentValidationAgent(offline_mode=True)
        
        sample_pdf_path = r'C:\Nirmal\GenAI\My Learning\Python programs\Agents\Sample_PDF\WC4803231023_Sample.pdf'
        generated_pdf_path = r'C:\Nirmal\GenAI\My Learning\Python programs\Agents\Generated PDF\WC 48 03 23_WCP000100020_01_0007252874_0000340043_Generated.pdf'
        
        print('📄 Testing Full Document Validation with Enhanced Static Text Analysis...')
        print(f'📁 Sample PDF: {sample_pdf_path}')
        print(f'📁 Generated PDF: {generated_pdf_path}')
        
        # Check if files exist
        if not os.path.exists(sample_pdf_path):
            print(f'❌ Sample PDF not found: {sample_pdf_path}')
            return
            
        if not os.path.exists(generated_pdf_path):
            print(f'❌ Generated PDF not found: {generated_pdf_path}')
            return
            
        # Run full validation
        print('\n🚀 Running full document validation...')
        result = await agent.validate_documents(sample_pdf_path, generated_pdf_path)
        
        # Display results
        print('\n📊 VALIDATION RESULTS:')
        print('=' * 80)
        print(f"Overall Score: {result.overall_score}/100")
        print(f"Static Text Score: {result.static_text_score}/100")
        print(f"Dynamic Field Score: {result.dynamic_field_score}/100")
        print(f"Validation Status: {result.validation_status}")
        print(f"Processing Time: {result.processing_time:.2f} seconds")
        
        print('\n🔍 STATIC TEXT DIFFERENCES:')
        print('=' * 80)
        if result.static_differences:
            for i, diff in enumerate(result.static_differences, 1):
                print(f"\n{i}. {diff.get('type', 'Unknown').upper()}:")
                print(f"   Sample: {diff.get('sample_text', 'N/A')}")
                print(f"   Generated: {diff.get('generated_text', 'N/A')}")
                print(f"   Location: {diff.get('location', 'N/A')}")
                print(f"   Severity: {diff.get('severity', 'N/A')}")
                print(f"   Impact: {diff.get('impact', 'N/A')}")
        else:
            print("✅ No static text differences found!")
        
        print('\n✅ POPULATED DYNAMIC FIELDS:')
        print('=' * 80)
        if result.populated_dynamic_fields:
            for i, field in enumerate(result.populated_dynamic_fields, 1):
                print(f"{i}. {field.get('field_name', 'Unknown')}: {field.get('field_value', 'N/A')} (Page {field.get('page_number', 'N/A')})")
        else:
            print("No populated fields found.")
        
        print('\n❌ BLANK DYNAMIC FIELDS:')
        print('=' * 80)
        if result.blank_dynamic_fields:
            for i, field in enumerate(result.blank_dynamic_fields, 1):
                print(f"{i}. {field.get('field_name', 'Unknown')}: {field.get('business_impact', 'N/A')} impact (Page {field.get('page_number', 'N/A')})")
        else:
            print("✅ No blank fields found!")
        
        print('\n💡 RECOMMENDATIONS:')
        print('=' * 80)
        if result.recommendations:
            for i, rec in enumerate(result.recommendations, 1):
                print(f"{i}. {rec}")
        else:
            print("✅ No recommendations - document is in good shape!")
        
        # Generate report
        print('\n📄 Generating detailed report...')
        report = agent.generate_report(result, "validation_report_enhanced.txt")
        print("✅ Report saved to: validation_report_enhanced.txt")
        
        return result
        
    except Exception as e:
        print(f'❌ Error during validation: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_full_validation())
