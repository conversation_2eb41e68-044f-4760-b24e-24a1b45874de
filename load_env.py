#!/usr/bin/env python3
"""
🔑 Environment Variable Loader for Fresh Document Validation System
Loads OpenAI API key from the .env file in the parent directory
"""

import os
import sys
from pathlib import Path

def load_environment_variables():
    """Load environment variables from .env file"""
    
    # Path to the .env file in the parent directory
    env_file_path = Path(__file__).parent.parent.parent / ".env"
    
    print(f" Looking for .env file at: {env_file_path}")
    
    if not env_file_path.exists():
        print(f"❌ .env file not found at: {env_file_path}")
        return False
    
    print(f"✅ .env file found!")
    
    # Read and parse the .env file
    try:
        with open(env_file_path, 'r') as f:
            lines = f.readlines()
        
        env_vars_loaded = 0
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines and comments
            if not line or line.startswith('#'):
                continue
            
            # Parse KEY=VALUE format
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # Remove quotes if present
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]
                
                # Set environment variable
                os.environ[key] = value
                env_vars_loaded += 1
                
                # Don't print the actual API key for security
                if 'API_KEY' in key or 'SECRET' in key or 'TOKEN' in key:
                    print(f"✅ Loaded {key}: {'*' * min(len(value), 20)}")
                else:
                    print(f"✅ Loaded {key}: {value}")
        
        print(f"✅ Successfully loaded {env_vars_loaded} environment variables")
        return True
        
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def check_openai_api_key():
    """Check if OpenAI API key is available"""
    
    api_key = os.getenv('OPENAI_API_KEY')
    
    if api_key:
        print(f"✅ OPENAI_API_KEY is set (length: {len(api_key)} characters)")
        return True
    else:
        print("❌ OPENAI_API_KEY is not set")
        return False

def setup_environment():
    """Complete environment setup for Fresh Document Validation System"""
    
    print("🔧 SETTING UP ENVIRONMENT FOR FRESH DOCUMENT VALIDATION")
    print("=" * 65)
    
    # Load environment variables
    env_loaded = load_environment_variables()
    
    if not env_loaded:
        print("\n⚠️ Could not load .env file. Checking system environment...")
    
    # Check OpenAI API key
    print("\n CHECKING OPENAI API KEY:")
    api_key_available = check_openai_api_key()
    
    if not api_key_available:
        print("\n❌ OpenAI API key not found!")
        print("Please ensure your .env file contains:")
        print("OPENAI_API_KEY=your-actual-api-key-here")
        return False
    
    print("\n✅ ENVIRONMENT SETUP COMPLETE!")
    print(" Fresh Document Validation System is ready to use!")
    return True

if __name__ == "__main__":
    setup_environment()
