#!/usr/bin/env python3
"""Analyze Generated PDF using the same process as Document_Validation_Agentic"""

import asyncio
import sys
import os

# Load environment variables
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    print("⚠️ load_env module not found - using system environment variables")

# Import from the validation agent
from Document_Validation_Agentic import DocumentValidationAgent

async def analyze_generated_pdf():
    try:
        # Initialize the validation agent in offline mode to use the same extraction logic
        agent = DocumentValidationAgent(offline_mode=True)
        
        pdf_path = r'C:\Nirmal\GenAI\My Learning\Python programs\Agents\Generated PDF\WC 48 03 23_WCP000100020_01_0007252874_0000340043_Generated.pdf'
        
        print('📄 Analyzing WC 48 03 23 Workers Comp PDF using Document_Validation_Agentic process...')
        print(f'📁 File path: {pdf_path}')
        
        # Check if file exists
        if not os.path.exists(pdf_path):
            print(f'❌ File not found: {pdf_path}')
            return
            
        # Step 1: Extract content using the same PDF processor
        print('\n📖 Step 1: Extracting PDF content with enhanced layout preservation...')
        content = await agent.pdf_processor.extract_content_with_layout(pdf_path)
        print(f'✅ Extraction complete: {len(content)} characters extracted')
        
        # Step 2: Detect dynamic fields using the same logic
        print('\n🎯 Step 2: Detecting dynamic fields using the same algorithm...')
        dynamic_analysis = await agent._detect_dynamic_fields(content)
        
        # Step 3: Display results
        print('\n📊 DYNAMIC FIELD ANALYSIS RESULTS:')
        print('=' * 80)

        print(f"Total Dynamic Fields Found: {dynamic_analysis.get('total_dynamic_fields', 0)}")
        print(f"Populated Fields Count: {dynamic_analysis.get('populated_fields_count', 0)}")
        print(f"Blank Fields Count: {dynamic_analysis.get('blank_fields_count', 0)}")
        print(f"Field Completeness Score: {dynamic_analysis.get('field_completeness_score', 0)}/100")
        print(f"Analysis Summary: {dynamic_analysis.get('analysis_summary', 'N/A')}")

        # Display populated fields
        populated_fields = dynamic_analysis.get('populated_fields', [])
        if populated_fields:
            print('\n✅ POPULATED FIELDS:')
            print('=' * 80)
            for i, field in enumerate(populated_fields, 1):
                print(f"\n{i}. {field.get('field_name', 'Unknown Field')}")
                print(f"   Type: {field.get('field_type', 'unknown')}")
                print(f"   Label: {field.get('field_label', 'N/A')}")
                print(f"   Value: {field.get('field_value', 'N/A')}")
                print(f"   Location: {field.get('location', 'N/A')}")
                print(f"   Page Number: {field.get('page_number', 'N/A')}")

        # Display blank fields
        blank_fields = dynamic_analysis.get('blank_fields', [])
        if blank_fields:
            print('\n❌ BLANK FIELDS:')
            print('=' * 80)
            for i, field in enumerate(blank_fields, 1):
                print(f"\n{i}. {field.get('field_name', 'Unknown Field')}")
                print(f"   Type: {field.get('field_type', 'unknown')}")
                print(f"   Label: {field.get('field_label', 'N/A')}")
                print(f"   Value: {field.get('field_value', 'N/A')}")
                print(f"   Location: {field.get('location', 'N/A')}")
                print(f"   Page Number: {field.get('page_number', 'N/A')}")
                print(f"   Blank Reason: {field.get('blank_reason', 'N/A')}")
                print(f"   Severity: {field.get('severity', 'N/A')}")

        if not populated_fields and not blank_fields:
            print("No dynamic fields detected.")
            
        # Step 4: Show extracted content for reference
        print('\n📄 EXTRACTED CONTENT (for reference):')
        print('=' * 80)
        print(content[:2000] + "..." if len(content) > 2000 else content)
        
    except Exception as e:
        print(f'❌ Error analyzing PDF: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(analyze_generated_pdf())
