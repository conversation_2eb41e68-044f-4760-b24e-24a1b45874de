#!/usr/bin/env python3
"""
🔧 FRESH DOCUMENT VALIDATION SYSTEM - CONFIGURATION

Centralized configuration for the Fresh Document Validation System.
Customize these settings to match your specific requirements.
"""

# =============================================================================
# LLM MODEL CONFIGURATION
# =============================================================================

# Primary model for document validation
# Options: "gpt-4o", "gpt-4-turbo", "gpt-4o-mini", "claude-3-5-sonnet"
DEFAULT_MODEL = "gpt-4o"

# Alternative models for different tasks (optional)
MODELS = {
    "static_text_analysis": "gpt-4o-mini",      # Cost-effective for template comparison
    "dynamic_field_detection": "gpt-4o",        # Advanced for field identification
    "blank_field_validation": "gpt-4o",         # Comprehensive for validation
}

# Model parameters
MODEL_TEMPERATURE = 0.1  # Low temperature for consistent results
MODEL_MAX_TOKENS = 4000  # Adjust based on document size

# =============================================================================
# VALIDATION SCORING CONFIGURATION
# =============================================================================

# Scoring weights (must sum to 1.0)
SCORING_WEIGHTS = {
    "static_text": 0.3,      # Weight for static text compliance
    "dynamic_fields": 0.4,   # Weight for dynamic field completeness
    "usability": 0.3         # Weight for document usability
}

# Score thresholds
SCORE_THRESHOLDS = {
    "pass": 90,      # Score >= 90 = PASS
    "warning": 70,   # Score >= 70 = WARNING
    "fail": 0        # Score < 70 = FAIL
}

# =============================================================================
# BLANK FIELD DETECTION CONFIGURATION
# =============================================================================

# Critical field types that must be populated
CRITICAL_FIELD_TYPES = [
    "policy_number",
    "policy_identifier", 
    "premium",
    "coverage_amount",
    "effective_date",
    "expiration_date"
]

# High priority field types
HIGH_PRIORITY_FIELD_TYPES = [
    "deductible",
    "underwriter",
    "agent_name",
    "insured_name"
]

# Medium priority field types
MEDIUM_PRIORITY_FIELD_TYPES = [
    "agent_phone",
    "agent_email",
    "producer_name"
]

# Blank field indicators to detect
BLANK_FIELD_INDICATORS = [
    "",           # Empty string
    " ",          # Single space
    "TBD",        # To be determined
    "N/A",        # Not applicable
    "PENDING",    # Pending value
    "___",        # Underscore placeholders
    "__/__/____", # Date placeholders
    "$_,___.__",  # Currency placeholders
    "___-___-____" # Phone placeholders
]

# =============================================================================
# PDF PROCESSING CONFIGURATION
# =============================================================================

# Enhanced PDF extraction settings
PDF_EXTRACTION = {
    "preserve_layout": True,
    "detect_visual_gaps": True,
    "gap_threshold_large": 30,    # Pixels for large gaps
    "gap_threshold_medium": 10,   # Pixels for medium gaps
    "gap_threshold_small": 3,     # Pixels for small gaps
    "line_grouping_tolerance": 1.0 # Y-coordinate tolerance for line grouping
}

# Fallback extraction settings
FALLBACK_EXTRACTION = {
    "use_layout": True,
    "x_tolerance": 1,
    "y_tolerance": 1
}

# =============================================================================
# STATIC TEXT ANALYSIS CONFIGURATION
# =============================================================================

# Static text elements to analyze
STATIC_TEXT_ELEMENTS = [
    "headers",
    "footers", 
    "section_titles",
    "labels",
    "legal_disclaimers",
    "regulatory_text",
    "form_numbers",
    "company_branding",
    "standard_text_blocks"
]

# Static text difference severity levels
STATIC_TEXT_SEVERITY = {
    "missing_legal_text": "CRITICAL",
    "missing_regulatory_text": "CRITICAL", 
    "spelling_errors": "HIGH",
    "formatting_differences": "MEDIUM",
    "minor_text_variations": "LOW"
}

# =============================================================================
# DYNAMIC FIELD DETECTION CONFIGURATION
# =============================================================================

# Dynamic field types to detect
DYNAMIC_FIELD_TYPES = [
    "policy_number",
    "policy_identifier",
    "name",
    "date", 
    "currency",
    "address",
    "phone",
    "email",
    "percentage",
    "coverage_amount",
    "deductible",
    "premium",
    "underwriter",
    "agent_name",
    "producer_name"
]

# Field detection patterns (regex patterns for specific field types)
FIELD_PATTERNS = {
    "policy_number": [
        r"policy\s+number[:\s]+([A-Z0-9\-]+)",
        r"policy\s+no[.:\s]+([A-Z0-9\-]+)",
        r"policy\s+#[:\s]+([A-Z0-9\-]+)"
    ],
    "currency": [
        r"\$[\d,]+\.?\d*",
        r"USD\s+[\d,]+\.?\d*",
        r"[\d,]+\.?\d*\s+dollars"
    ],
    "date": [
        r"\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}",
        r"\d{4}[/\-]\d{1,2}[/\-]\d{1,2}",
        r"[A-Za-z]+\s+\d{1,2},?\s+\d{4}"
    ],
    "phone": [
        r"\(\d{3}\)\s*\d{3}[.\-]?\d{4}",
        r"\d{3}[.\-]\d{3}[.\-]\d{4}"
    ]
}

# =============================================================================
# REPORTING CONFIGURATION
# =============================================================================

# Report format settings
REPORT_FORMAT = {
    "include_timestamp": True,
    "include_processing_time": True,
    "include_detailed_scores": True,
    "include_recommendations": True,
    "include_next_steps": True,
    "max_differences_shown": 10,  # Limit number of differences in report
    "max_blank_fields_shown": 20  # Limit number of blank fields in report
}

# Report file settings
REPORT_FILES = {
    "auto_save": True,
    "filename_format": "validation_report_{timestamp}.txt",
    "timestamp_format": "%Y%m%d_%H%M%S",
    "encoding": "utf-8"
}

# =============================================================================
# BUSINESS IMPACT CONFIGURATION
# =============================================================================

# Business impact descriptions for different field types
BUSINESS_IMPACT = {
    "CRITICAL": {
        "description": "High - Critical policy information missing, may cause legal/compliance issues",
        "action_required": "Immediate",
        "escalation": "Management notification required"
    },
    "HIGH": {
        "description": "Medium-High - Important information missing, may affect customer understanding",
        "action_required": "Within 24 hours", 
        "escalation": "Team lead notification"
    },
    "MEDIUM": {
        "description": "Medium - Contact information missing, may impact customer service",
        "action_required": "Within 48 hours",
        "escalation": "Standard workflow"
    },
    "LOW": {
        "description": "Low - Optional information missing, minimal impact",
        "action_required": "Next business day",
        "escalation": "None required"
    }
}

# =============================================================================
# REGULATORY COMPLIANCE CONFIGURATION
# =============================================================================

# Regulatory compliance risk levels
REGULATORY_RISK = {
    "HIGH": [
        "policy_number",
        "premium", 
        "coverage_amount",
        "effective_date",
        "expiration_date",
        "legal_disclaimers"
    ],
    "MEDIUM": [
        "deductible",
        "underwriter",
        "agent_information"
    ],
    "LOW": [
        "producer_information",
        "contact_details"
    ]
}

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Processing timeouts (seconds)
TIMEOUTS = {
    "pdf_extraction": 60,
    "static_analysis": 120,
    "dynamic_detection": 180,
    "blank_validation": 120,
    "total_validation": 600
}

# Retry settings
RETRY_SETTINGS = {
    "max_retries": 3,
    "retry_delay": 5,  # seconds
    "exponential_backoff": True
}

# =============================================================================
# DEBUGGING AND LOGGING CONFIGURATION
# =============================================================================

# Debug settings
DEBUG = {
    "enabled": False,
    "verbose_logging": False,
    "save_intermediate_results": False,
    "print_ai_prompts": False,
    "print_ai_responses": False
}

# Logging configuration
LOGGING = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "validation.log",
    "max_file_size": "10MB",
    "backup_count": 5
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

def validate_config():
    """Validate configuration settings"""
    
    # Check scoring weights sum to 1.0
    weight_sum = sum(SCORING_WEIGHTS.values())
    if abs(weight_sum - 1.0) > 0.001:
        raise ValueError(f"Scoring weights must sum to 1.0, got {weight_sum}")
    
    # Check score thresholds are in correct order
    if not (SCORE_THRESHOLDS["fail"] <= SCORE_THRESHOLDS["warning"] <= SCORE_THRESHOLDS["pass"]):
        raise ValueError("Score thresholds must be in ascending order")
    
    # Check model name is valid
    valid_models = ["gpt-4o", "gpt-4-turbo", "gpt-4o-mini", "claude-3-5-sonnet"]
    if DEFAULT_MODEL not in valid_models:
        raise ValueError(f"Invalid model: {DEFAULT_MODEL}. Must be one of {valid_models}")
    
    return True

# Validate configuration on import
if __name__ == "__main__":
    try:
        validate_config()
        print("✅ Configuration validation passed")
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
else:
    validate_config()
