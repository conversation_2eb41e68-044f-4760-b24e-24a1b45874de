#!/usr/bin/env python3
"""
Run main Document_Validation_Agentic.py with Azure API Management configuration
"""

import os
import subprocess
import sys

def main():
    print("🔧 SETTING UP AZURE API MANAGEMENT FOR MAIN SCRIPT")
    print("=" * 60)
    
    # Read the actual configuration from test_azure_config.py
    try:
        with open('test_azure_config.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Extract the actual values (basic parsing)
        subscription_key = None
        api_url = None
        
        for line in content.split('\n'):
            if 'SUBSCRIPTION_KEY =' in line and '"' in line:
                subscription_key = line.split('"')[1]
            elif 'API_URL =' in line and '"' in line:
                api_url = line.split('"')[1]
        
        if subscription_key and api_url:
            print(f"✅ Found Azure configuration:")
            print(f"   🔑 Subscription Key: {subscription_key[:8]}...")
            print(f"   🌐 API URL: {api_url}")
            
            # Set environment variables
            os.environ['SUBSCRIPTION_KEY'] = subscription_key
            os.environ['API_URL'] = api_url
            
            print("\n🚀 Running main script with Azure API Management...")
            print("=" * 60)
            
            # Run the main script
            result = subprocess.run([sys.executable, 'Document_Validation_Agentic.py'], 
                                  capture_output=False, text=True)
            
            return result.returncode == 0
            
        else:
            print("❌ Could not extract Azure configuration from test_azure_config.py")
            print("💡 Make sure test_azure_config.py has SUBSCRIPTION_KEY and API_URL set")
            return False
            
    except FileNotFoundError:
        print("❌ test_azure_config.py not found")
        print("💡 Please make sure you have the test configuration file")
        return False
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Main script completed successfully with Azure API Management!")
    else:
        print("\n❌ Main script failed or configuration issue")
