#!/usr/bin/env python3
"""
Debug script to analyze what layout differences <PERSON> should be detecting
"""

import asyncio
import os
from Document_Validation_Agentic import EnhancedPDFProcessor

async def debug_layout_differences():
    """Extract and compare PDF content to see what <PERSON> should detect"""
    
    processor = EnhancedPDFProcessor()
    
    # File paths
    sample_pdf = "sample/WC4803231023.pdf"
    generated_pdf = "generated/WC 48 03 23_WCP000100020_01_0007252874_0000340043_Gen1.pdf"
    
    print("🔍 DEBUGGING LAYOUT DIFFERENCES")
    print("=" * 60)
    
    try:
        # Extract content from both PDFs
        print("📖 Extracting PDF content...")
        sample_content = await processor.extract_content_with_layout(sample_pdf)
        generated_content = await processor.extract_content_with_layout(generated_pdf)
        
        print(f"✅ Sample PDF: {len(sample_content)} characters")
        print(f"✅ Generated PDF: {len(generated_content)} characters")
        print()
        
        # Split into lines for analysis
        sample_lines = sample_content.split('\n')
        generated_lines = generated_content.split('\n')
        
        print("📄 SAMPLE PDF CONTENT (First 20 lines):")
        print("-" * 40)
        for i, line in enumerate(sample_lines[:20]):
            print(f"{i+1:2d}: {line}")
        
        print("\n📄 GENERATED PDF CONTENT (First 20 lines):")
        print("-" * 40)
        for i, line in enumerate(generated_lines[:20]):
            print(f"{i+1:2d}: {line}")
        
        print("\n🔍 LINE-BY-LINE COMPARISON (First 20 lines):")
        print("-" * 60)
        max_lines = min(len(sample_lines), len(generated_lines), 20)
        
        for i in range(max_lines):
            sample_line = sample_lines[i].strip()
            generated_line = generated_lines[i].strip()
            
            if sample_line != generated_line:
                print(f"❌ Line {i+1} DIFFERS:")
                print(f"   Sample:    '{sample_line}'")
                print(f"   Generated: '{generated_line}'")
                print()
            else:
                print(f"✅ Line {i+1}: SAME")
        
        # Check for heading differences specifically
        print("\n🎯 HEADING ANALYSIS:")
        print("-" * 30)
        
        # Look for lines that might be headings (short lines, all caps, etc.)
        sample_headings = []
        generated_headings = []
        
        for i, line in enumerate(sample_lines[:10]):
            line = line.strip()
            if line and (line.isupper() or len(line) < 50):
                sample_headings.append((i+1, line))
        
        for i, line in enumerate(generated_lines[:10]):
            line = line.strip()
            if line and (line.isupper() or len(line) < 50):
                generated_headings.append((i+1, line))
        
        print("Sample headings:")
        for line_num, heading in sample_headings:
            print(f"  Line {line_num}: '{heading}'")
        
        print("\nGenerated headings:")
        for line_num, heading in generated_headings:
            print(f"  Line {line_num}: '{heading}'")
        
        # Check for visual gaps
        print(f"\n📏 VISUAL GAP ANALYSIS:")
        print("-" * 25)
        sample_gaps = sample_content.count('[VISUAL_GAP]')
        generated_gaps = generated_content.count('[VISUAL_GAP]')
        print(f"Sample visual gaps: {sample_gaps}")
        print(f"Generated visual gaps: {generated_gaps}")
        print(f"Difference: {abs(sample_gaps - generated_gaps)}")
        
        # Check page structure
        print(f"\n📄 PAGE STRUCTURE:")
        print("-" * 20)
        sample_pages = sample_content.count('--- Page')
        generated_pages = generated_content.count('--- Page')
        print(f"Sample pages: {sample_pages}")
        print(f"Generated pages: {generated_pages}")
        
        # Save full content for manual inspection
        with open("debug_sample_content.txt", "w", encoding="utf-8") as f:
            f.write(sample_content)
        
        with open("debug_generated_content.txt", "w", encoding="utf-8") as f:
            f.write(generated_content)
        
        print(f"\n💾 Full content saved to:")
        print(f"   - debug_sample_content.txt")
        print(f"   - debug_generated_content.txt")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_layout_differences())
