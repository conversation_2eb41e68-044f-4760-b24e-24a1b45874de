#!/usr/bin/env python3
"""Test static text analysis specifically"""

import asyncio
import sys
import os

# Load environment variables
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    print("⚠️ load_env module not found - using system environment variables")

# Import from the validation agent
from Document_Validation_Agentic import DocumentValidationAgent

async def test_static_analysis():
    try:
        # Initialize the validation agent in offline mode
        agent = DocumentValidationAgent(offline_mode=True)
        
        sample_pdf_path = r'C:\Nirmal\GenAI\My Learning\Python programs\Agents\Sample_PDF\WC4803231023_Sample.pdf'
        generated_pdf_path = r'C:\Nirmal\GenAI\My Learning\Python programs\Agents\Generated PDF\WC 48 03 23_WCP000100020_01_0007252874_0000340043_Generated.pdf'
        
        print('🔍 Testing Static Text Analysis Only...')
        print(f'📁 Sample PDF: {sample_pdf_path}')
        print(f'📁 Generated PDF: {generated_pdf_path}')
        
        # Check if files exist
        if not os.path.exists(sample_pdf_path):
            print(f'❌ Sample PDF not found: {sample_pdf_path}')
            return
            
        if not os.path.exists(generated_pdf_path):
            print(f'❌ Generated PDF not found: {generated_pdf_path}')
            return
            
        # Extract content from both PDFs
        print('\n📖 Extracting PDF content...')
        sample_content = await agent.pdf_processor.extract_content_with_layout(sample_pdf_path)
        generated_content = await agent.pdf_processor.extract_content_with_layout(generated_pdf_path)
        
        print(f'✅ Sample PDF: {len(sample_content)} characters extracted')
        print(f'✅ Generated PDF: {len(generated_content)} characters extracted')
        
        # Run static text analysis
        print('\n🔍 Running static text analysis...')
        static_analysis = await agent._analyze_static_text(sample_content, generated_content)
        
        # Display results
        print('\n📊 STATIC TEXT ANALYSIS RESULTS:')
        print('=' * 80)
        print(f"Static Text Score: {static_analysis.get('static_text_score', 0)}/100")
        print(f"Compliance Status: {static_analysis.get('compliance_status', 'Unknown')}")
        print(f"Static Elements Analyzed: {static_analysis.get('static_elements_analyzed', 0)}")
        print(f"Analysis Summary: {static_analysis.get('analysis_summary', 'N/A')}")
        
        print('\n🔍 STATIC TEXT DIFFERENCES:')
        print('=' * 80)
        differences = static_analysis.get('static_differences', [])
        if differences:
            for i, diff in enumerate(differences, 1):
                print(f"\n{i}. {diff.get('type', 'Unknown').upper()}:")
                print(f"   Sample: \"{diff.get('sample_text', 'N/A')}\"")
                print(f"   Generated: \"{diff.get('generated_text', 'N/A')}\"")
                print(f"   Location: {diff.get('location', 'N/A')}")
                print(f"   Severity: {diff.get('severity', 'N/A')}")
                print(f"   Impact: {diff.get('impact', 'N/A')}")
        else:
            print("✅ No static text differences found!")
        
        # Show sample of content for debugging
        print('\n📄 SAMPLE CONTENT (first 500 chars):')
        print('=' * 80)
        print(sample_content[:500])
        print('\n📄 GENERATED CONTENT (first 500 chars):')
        print('=' * 80)
        print(generated_content[:500])
        
        return static_analysis
        
    except Exception as e:
        print(f'❌ Error during static analysis: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_static_analysis())
