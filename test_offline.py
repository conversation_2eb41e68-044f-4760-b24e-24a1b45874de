#!/usr/bin/env python3
"""
🔧 Test Offline Mode
Test the document validation system in offline mode
"""

import asyncio
import os

# Load environment variables
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    print("⚠️ load_env module not found - using system environment variables")

from Document_Validation import demo_validation

async def main():
    """Test offline mode"""
    print("🧪 TESTING OFFLINE MODE")
    print("=" * 50)
    
    # Test with force offline mode
    result = await demo_validation(force_offline=True)
    
    if result:
        print("\n✅ Offline mode test successful!")
        print(f"   Overall Score: {result.overall_score}/100")
        print(f"   Status: {result.validation_status}")
    else:
        print("\n❌ Offline mode test failed")

if __name__ == "__main__":
    asyncio.run(main())
