#!/usr/bin/env python3
"""Extract content from generated PDF"""

import asyncio
import sys
import os

# Load environment variables
try:
    from load_env import setup_environment
    setup_environment()
except ImportError:
    print("⚠️ load_env module not found - using system environment variables")

# Import the PDF processor from the validation agent
from Document_Validation_Agentic import EnhancedPDFProcessor

async def extract_generated_pdf():
    try:
        processor = EnhancedPDFProcessor()
        pdf_path = '../Generated PDF/WC 48 03 23_WCP000100020_01_0007252874_0000340043_Generated.pdf'
        
        print('📄 Extracting Generated PDF content...')
        print(f'📁 File path: {pdf_path}')
        
        # Check if file exists
        if not os.path.exists(pdf_path):
            print(f'❌ File not found: {pdf_path}')
            return
            
        content = await processor.extract_content_with_layout(pdf_path)
        
        print(f'✅ Extraction complete: {len(content)} characters extracted')
        print('=' * 80)
        print('EXTRACTED CONTENT:')
        print('=' * 80)
        print(content)
        
    except Exception as e:
        print(f'❌ Error extracting PDF: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(extract_generated_pdf())
