 DOCUMENT VALIDATION REPORT
============================================================
Validation Date: 2025-07-16T12:37:34.817535
Processing Time: 70.62 seconds
Overall Score: 68/100
Validation Status: FAIL

 DETAILED SCORES:
   Static Text Compliance: 95/100
   Dynamic Field Completeness: 84/100
   XML Field Mapping Completeness: 46/100
   Layout Structure Consistency: 50/100

 STATIC TEXT ANALYSIS:
   Differences Found: 0
 POPULATED DYNAMIC FIELDS ANALYSIS:
   Total Populated Fields: 11

   1. POLICY NUMBER:
      Type: policy_number
      Value: WCP000100020-1
      Location: Page 1
      Page Number: 1

   2. NCCI INSURANCE COMPANY NUMBER:
      Type: ncci_number
      Value: 11037
      Location: Page 1
      Page Number: 1

   3. INSURANCE COMPANY:
      Type: company_name
      Value: HIGH WEST CASUALTY COMPANY
      Location: Page 1
      Page Number: 1

   4. <PERSON><PERSON>ORSEMENT NUMBER:
      Type: endorsement_number
      Value: 0007252874
      Location: Page 1
      Page Number: 1

   5. DATE:
      Type: date
      Value: 04/03/2025
      Location: Page 1
      Page Number: 1

   6. AGENT/BROKER:
      Type: agent_broker
      Value: 1024 ARTHUR J GALLAGHER RISK MGMT SVCS INC
      Location: Page 1
      Page Number: 1

   7. COMPANY NAME:
      Type: company_name
      Value: HIGH WEST CASUALTY COMPANY
      Location: Page 1
      Page Number: 1

   8. ORGANIZATION NAME:
      Type: organization_name
      Value: ARTHUR J GALLAGHER RISK MGMT SVCS INC
      Location: Page 1
      Page Number: 1

   9. NAMED INSURED:
      Type: named_insured
      Value: WI Wisconsin Designated
      Location: Page 1 (Tabular layout)
      Page Number: 1

   10. ENDORSEMENT EFFECTIVE DATE:
      Type: endorsement_effective_date
      Value: 04/03/2025
      Location: Page 1 (Tabular layout)
      Page Number: 1

   11. NAME AND ADDRESS OF DESIGNATED NAMED INSURED:
      Type: name_address_of_designated_named_insured
      Value: Test
      Location: Page 1
      Page Number: 1

 BLANK DYNAMIC FIELDS ANALYSIS:
   Total Blank Fields: 2
   Critical Issues: 2

   1. EFFECTIVE DATE OF CANCELLATION:
      Type: effective_date_of_cancellation
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Effective Date of Cancellation field is blank
      Recommendation: Populate Effective Date of Cancellation with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   2. NAME AND ADDRESS OF FIRST NAMED INSURED:
      Type: name_address_of_first_named_insured
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Name and Address of First Named Insured field is blank
      Recommendation: Populate Name and Address of First Named Insured with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

 XML FIELD MAPPING ANALYSIS:
   XML Analysis Summary: Searched for 13 dynamic field values in XML: 6 exact matches found, 2 blank fields, 5 values not found
   Total Field Mappings: 13
   XML Completeness Score: 46/100

   ✅ Consistent Mappings: 0
   ❌ Inconsistent Mappings: 0

   1. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: NOT_FOUND
      XML Value: N/A
      Consistency: Unknown
      Criticality: CRITICAL

   2. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: NOT_FOUND
      XML Value: N/A
      Consistency: Unknown
      Criticality: LOW

   3. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: NOT_FOUND
      XML Value: N/A
      Consistency: Unknown
      Criticality: LOW

   4. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: NOT_FOUND
      XML Value: N/A
      Consistency: Unknown
      Criticality: LOW

   5. ❌ UNKNOWN FIELD:
      PDF Value: N/A
      XML Path: PolicyPeriod/DisplayName
      XML Value: N/A
      Consistency: Unknown
      Criticality: LOW

   ... and 8 more field mappings

📐 LAYOUT STRUCTURE ANALYSIS:
   Layout Analysis Summary: Found 8 layout differences with score 50/100
   Total Layout Differences: 11
   Layout Score: 50/100

   🚨 Critical Layout Issues: 0
   ⚠️ High Priority Issues: 11

   1. ⚠️ DOCUMENT_LENGTH:
      Location: Overall document structure
      Sample Layout: 94 content lines
      Generated Layout: 36 content lines
      Severity: HIGH
      Visual Impact: Significant difference in document size and structure
      Peter's Comment: Document length mismatch: 58 line difference indicates structural changes

   2. ⚠️ CONTENT_POSITIONING:
      Location: NCCI Info
      Sample Layout: Line 9
      Generated Layout: Line 3
      Severity: HIGH
      Visual Impact: Content repositioned affects document flow
      Peter's Comment: NCCI Info: Line 9 → 3 (6 lines ↑)

   3. ⚠️ CONTENT_POSITIONING:
      Location: State Designation
      Sample Layout: Line 11
      Generated Layout: Line 5
      Severity: HIGH
      Visual Impact: Content repositioned affects document flow
      Peter's Comment: State Designation: Line 11 → 5 (6 lines ↑)

   4. ⚠️ CONTENT_POSITIONING:
      Location: Endorsement Type
      Sample Layout: Line 12
      Generated Layout: Line 6
      Severity: HIGH
      Visual Impact: Content repositioned affects document flow
      Peter's Comment: Endorsement Type: Line 12 → 6 (6 lines ↑)

   5. ⚠️ CONTENT_POSITIONING:
      Location: Policy Type
      Sample Layout: Line 14
      Generated Layout: Line 8
      Severity: HIGH
      Visual Impact: Content repositioned affects document flow
      Peter's Comment: Policy Type: Line 14 → 8 (6 lines ↑)

   ... and 6 more layout differences

💡 RECOMMENDATIONS:
   1. URGENT: Populate 2 critical blank fields
   2. Address regulatory compliance risks immediately
   3. URGENT: Resolve 2 critical XML field mapping issues
   4. Investigate 5 field values not found in XML
   5. Address 2 blank fields in PDF
   6. Validate XML data completeness for missing values
   7. Ensure proper data synchronization between PDF generation and XML source
   8. Review 11 layout differences for consistency
   9. Review layout differences for consistency

============================================================
🎯 NEXT STEPS:
1. Address critical blank fields immediately
2. Fix static text differences
3. Resolve XML field mapping inconsistencies
4. Validate regulatory compliance
5. Re-run validation after fixes
============================================================